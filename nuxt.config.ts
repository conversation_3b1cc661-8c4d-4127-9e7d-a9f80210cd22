// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-09-22",
  app: {
    head: {
      title: "SK Mobile School",
      titleTemplate: "%s",
      htmlAttrs: {
        lang: "en",
      },
      script: [
        {
          src: `https://www.google.com/recaptcha/enterprise.js?render=${process.env.NUXT_PUBLIC_RECAPTCHA_SITE_KEY}`,
          async: true,
          defer: true,
        },
        {
          src: "https://js.pusher.com/8.0.1/pusher.min.js",
          async: true,
          defer: true,
        },
      ],
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        { name: "keywords", content: process.env.NUXT_PUBLIC_META_KEYWORDS },
        { property: "og:type", content: "website" },
        {
          name: "description",
          content:
            "SK Mobile School - A bilingual learning platform offering interactive education in Bengali and English. Access quality education anytime, anywhere through our mobile-first learning experience.",
        },
        {
          key: "og:image",
          property: "og:image",
          content: `${process.env.NUXT_PUBLIC_SITE_URL}/images/logo.webp`,
        },
      ],
      link: [
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
        },
        {
          rel: "icon",
          type: "image/x-icon",
          href: `${process.env.NUXT_PUBLIC_SITE_URL}/images/favicon.ico`,
        },
      ],
    },
    pageTransition: { name: "page", mode: "out-in" },
    layoutTransition: { name: "layout", mode: "out-in" },
  },

  runtimeConfig: {
    apiUrl: process.env.NUXT_PUBLIC_API_URL,
    authSecret: "secret",
    public: {
      appName: process.env.NUXT_PUBLIC_APP_NAME,
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL,
      apiUrl: process.env.NUXT_PUBLIC_API_URL,
      googleMapKey: process.env.NUXT_PUBLIC_GOOGLE_MAP_KEY,
      workflow: process.env.NUXT_PUBLIC_WORKFLOW,
      recaptchaKey: process.env.NUXT_PUBLIC_RECAPTCHA_SITE_KEY,
      pusherAppKey: process.env.NUXT_PUBLIC_PUSHER_APP_KEY,
      pusherHost: process.env.NUXT_PUBLIC_PUSHER_HOST,
      pusherPort: process.env.NUXT_PUBLIC_PUSHER_PORT,
      gtm: {
        id: process.env.NUXT_PUBLIC_GTAG_ID || "",
      },
    },
  },

  devtools: { enabled: true },

  css: ["~/assets/css/main.css", "v-calendar/dist/style.css"],

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  modules: [
    "dayjs-nuxt",
    "@pinia/nuxt",
    "@nuxtjs/i18n",
    "@vueuse/nuxt",
    "@vee-validate/nuxt",
    "@zadigetvoltaire/nuxt-gtm",
    "@nuxtjs/seo",
  ],

  dayjs: {
    plugins: ["relativeTime", "utc", "timezone"],
    defaultTimezone: "Dhaka",
  },

  veeValidate: {
    autoImports: true,
    componentNames: {
      Form: "VeeForm",
      Field: "VeeField",
      FieldArray: "VeeFieldArray",
      ErrorMessage: "VeeErrorMessage",
    },
  },

  i18n: {
    locales: [
      {
        code: "bn",
        name: "Bengali",
        emoji: "🇧🇩",
        file: "bn-BD.json",
      },
      {
        code: "en",
        name: "English",
        emoji: "🇺🇸",
        file: "en-US.json",
      },
    ],
    langDir: "lang",
    defaultLocale: "bn",
    detectBrowserLanguage: false,
  },

  nitro: {
    routeRules: {
      "/server/**": {
        proxy: `${process.env.NUXT_PUBLIC_API_URL}/**`,
      },
    },
  },
});

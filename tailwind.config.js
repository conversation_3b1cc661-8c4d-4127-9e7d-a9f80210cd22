/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./nuxt.config.{js,ts}",
    "./app.vue",
  ],
  theme: {
    extend: {
      screens: {
        xs: "400px",
        dx: "1366px",
        "2dx": "1440px",
        "3xl": "1920px",
        "4xl": "2100px",
      },
      colors: {
        "primary-red": "#EC1F27",
      },
      spacing: {},
      borderRadius: {
        "5px": "5px",
        "100px": "100px",
      },
      fontFamily: {
        "noto-sans-bengali": ["Noto Sans Bengali", "sans-serif"],
        roboto: ["Roboto", "sans-serif"],
      },
    },
  },
  plugins: [],
};

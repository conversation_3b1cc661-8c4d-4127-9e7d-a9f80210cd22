<script setup>
const { FOOTER_COURSE } = useUrls();
const localePath = useLocalePath();

const { data: footerCourseRes } = await useFetch(FOOTER_COURSE);
const footerCourse = computed(() => footerCourseRes.value?.data);
</script>

<template>
  <section>
    <div class="custom-container pb-32">
      <div
        class="top-footer p-6 md:py-12 lg:px-16 flex flex-col-reverse lg:flex-row items-center justify-between gap-10 lg:gap-0"
      >
        <div class="max-w-[640px] flex flex-col gap-6">
          <p class="text-black text-xl lg:text-2xl">{{ $t("footer_text") }}</p>
          <BaseGroupButton wrapperClass="flex-wrap xl:flex-nowrap" />
        </div>
        <img
          src="~/assets/img/footer/device.webp"
          alt="Sk Mobile School Device"
          class="w-full max-w-min aspect-[357/308]"
        />
      </div>
    </div>

    <footer class="custom-container text-center text-neutral-600 md:text-left">
      <div class="pt-[18px] pb-[10px] text-center md:text-left">
        <div class="grid-1 grid gap-8 md:grid-cols-4">
          <div class="">
            <img
              class="w-[115px] h-[91.28px] 2dx:w-[145px] 2dx:h-[101px] mx-auto md:mx-0"
              src="/images/logo.webp"
              alt="Sk Mobile School Logo"
            />
            <h6
              class="font-medium text-base text-[#EC1F27] leading-9 xs:pt-5 md:pt-[30px] mx-auto md:mx-0"
            >
              {{ $t("download_app") }}
            </h6>

            <div
              class="flex gap-[10px] md:pt-[13px] mx-auto md:mx-0 justify-center items-center md:justify-start"
            >
              <div>
                <NuxtLink
                  to="https://play.google.com/store/apps/details?id=com.devxhub.skmobileschool"
                  target="_blank"
                  rel="noopener"
                >
                  <img
                    class="w-[127px] h-10"
                    src="/images/homepage/googlePlayStore.svg"
                    alt="googlePlayStore"
                  />
                </NuxtLink>
              </div>
              <div>
                <NuxtLink to="/">
                  <img
                    class="w-[127px] h-10"
                    src="/images/homepage/appStore.svg"
                    alt="appStore"
                  />
                </NuxtLink>
              </div>
            </div>
          </div>
          <!-- Products section -->
          <div class="md:pl-[17px] pl-0">
            <h5
              class="md:text-xl font-semibold uppercase leading-7 text-primary-red pb-3 2dx:pb-[14px]"
            >
              {{ $t("all_courses") }}
            </h5>
            <ul
              class="list-style-none flex flex-col pl-0 space-y-2"
              data-te-navbar-nav-ref
            >
              <li
                v-for="(course, index) in footerCourse"
                :key="course.id"
                class="font-normal text-base leading-7 text-[#222222]"
                data-te-nav-item-ref
              >
                <NuxtLink
                  class="line-clamp-1 footer-link"
                  aria-current="page"
                  :to="localePath(`/precheckout/${course.slug}`)"
                  data-te-nav-link-ref
                  >{{ course.title }}
                </NuxtLink>
              </li>
            </ul>
          </div>
          <!-- Useful links section -->
          <div class="mx-auto">
            <h6
              class="mb-[12px] md:mb-[9px] 2dx:mb-5 flex font-semibold text-xl text-primary-red uppercase justify-center items-center md:justify-start"
            >
              {{ $t("ceo_cto") }}
            </h6>
            <div>
              <NuxtLink>
                <img
                  src="/images/homepage/model.webp"
                  class="w-[206px] aspect-[103/115]"
                  alt="ceo and cto of sk mobile school"
                />
              </NuxtLink>
            </div>
          </div>
          <!-- Contact section -->
          <div class="flex text-center justify-center md:justify-end">
            <div class="text-center">
              <div class="">
                <h6
                  class="pb-[14px] flex justify-center md:justify-start md:text-xl uppercase leading-7 text-primary-red !font-semibold footer-link"
                >
                  {{ $t("company") }}
                </h6>
                <ul
                  class="list-style-none flex flex-col pl-0 space-y-2 font-medium"
                  data-te-navbar-nav-ref
                >
                  <li
                    class="flex justify-center md:justify-start md:text-base font-normal leading-7 text-[#222222]"
                    data-te-nav-item-ref
                  >
                    <NuxtLink
                      class="footer-link"
                      aria-current="page"
                      :to="localePath('/')"
                      data-te-nav-link-ref
                      >{{ $t("home") }}
                    </NuxtLink>
                  </li>
                  <li class="footer-li" data-te-nav-item-ref>
                    <NuxtLink
                      class="footer-link"
                      aria-current="page"
                      :to="localePath('/firmware')"
                      data-te-nav-link-ref
                      >{{ $t("download") }}
                    </NuxtLink>
                  </li>

                  <li class="footer-li" data-te-nav-item-ref>
                    <NuxtLink
                      class="footer-link"
                      aria-current="page"
                      :to="localePath('/refund-policy')"
                      data-te-nav-link-ref
                      >{{ $t("refund_policy") }}
                    </NuxtLink>
                  </li>
                  <li class="footer-li" data-te-nav-item-ref>
                    <NuxtLink
                      class="footer-link"
                      aria-current="page"
                      :to="localePath('/cancellation-policy')"
                      data-te-nav-link-ref
                      >{{ $t("cancellation_policy") }}
                    </NuxtLink>
                  </li>
                  <li class="footer-li" data-te-nav-item-ref>
                    <NuxtLink
                      class="footer-link flex items-start justify-start space-x-2"
                      aria-current="page"
                      :to="localePath('/contact-us')"
                      data-te-nav-link-ref
                      ><span class="font-bold">{{
                        $t("click_to_contact")
                      }}</span>
                      <img
                        src="~/assets/img/icon/rocket.png"
                        alt="Sk Mobile Schoo Contact us"
                      />
                    </NuxtLink>
                  </li>
                </ul>
              </div>
              <div class="flex gap-[14px] md:justify-start pt-4 pb-10">
                <NuxtLink
                  target="_blank"
                  aria-label="Sk Mobile School Facebook"
                  to="https://www.facebook.com/share/1A3GyUuSrN/?mibextid=wwXIfr"
                >
                  <img
                    src="/images/homepage/facebook.svg"
                    alt="Sk Mobile School Facebook"
                    class="w-10 aspect-square"
                  />
                </NuxtLink>
                <NuxtLink
                  target="_blank"
                  aria-label="Sk Mobile School Instagram"
                  to="https://instagram.com/shohidullakwesar?igshid=YWYwM2I1ZDdmOQ=="
                >
                  <img
                    src="/images/homepage/instagram.svg"
                    alt="Sk Mobile School Instagram"
                    class="w-10 aspect-square"
                  />
                </NuxtLink>
                <NuxtLink
                  target="_blank"
                  aria-label="Sk Mobile School YouTube"
                  to="https://www.youtube.com/@SkMobileSchool1"
                >
                  <img
                    src="/images/homepage/youtube.svg"
                    alt="Sk Mobile School YouTube"
                    class="w-10 aspect-square"
                  />
                </NuxtLink>
                <NuxtLink
                  target="_blank"
                  aria-label="Sk Mobile School Telegram"
                  to="https://t.me/skmobileschool"
                >
                  <img
                    src="/images/homepage/telegram.svg"
                    alt="Sk Mobile School Telegram"
                    class="w-10 aspect-square"
                  />
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center flex-wrap gap-2">
        <img
          src="/images/billing-methods/dbbl-nexus.svg"
          alt="sk mobile school dbbl-nexus"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/visa.svg"
          alt="sk mobile school visa"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/master-card.svg"
          alt="sk mobile school master-card"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/amex.svg"
          alt="sk mobile school amex"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/rocket.svg"
          alt="sk mobile school rocket"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/bkash.svg"
          alt="sk mobile school bkash"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/nagad.svg"
          alt="sk mobile school nagad"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/upay.svg"
          alt="sk mobile school upay"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/tap.svg"
          alt="sk mobile school tap"
          class="h-8 md:h-9 aspect-video"
        />
        <img
          src="/images/billing-methods/wallet.svg"
          alt="sk mobile school wallet"
          class="h-8 md:h-9 aspect-video"
        />
      </div>
      <div class="w-full h-[1px] bg-black mt-3"></div>

      <!--Copyright section-->
      <div
        class="flex flex-col-reverse justify-center items-center md:flex-row md:justify-between md:py-[13px] 2dx:py-5 gap-2 pt-[20px] pb-5"
      >
        <div
          class="flex items-center flex-col md:flex-row gap-2 font-normal text-xs lg:text-sm 2dx:text-base"
        >
          <h6 class="">
            © 2023.
            <span class="text-primary-red">{{ $t("site_name") }}</span>
            {{ $t("rights_reserved") }}
          </h6>
          <span class="hidden md:block">|</span>
          <p>
            {{ $t("developed_by") }}
            <NuxtLink
              class="text-primary-red"
              to="https://devxhub.com"
              target="_blank"
              rel="noopener"
            >
              DEVxHUB</NuxtLink
            >
          </p>
        </div>

        <div class="flex gap-2 font-normal text-xs lg:text-sm 2dx:text-base">
          <div>
            <NuxtLink
              class="footer-link"
              aria-current="page"
              :to="localePath('/about-us')"
              data-te-nav-link-ref
              >{{ $t("about_us") }}
            </NuxtLink>
          </div>
          <span>|</span>
          <div>
            <NuxtLink
              class="footer-link"
              aria-current="page"
              :to="localePath('/privacy-policy')"
              data-te-nav-link-ref
              >{{ $t("privacy_policy") }}
            </NuxtLink>
          </div>
          <span>|</span>
          <div>
            <NuxtLink
              class="footer-link"
              aria-current="page"
              :to="localePath('/terms-of-use')"
              data-te-nav-link-ref
              >{{ $t("terms_conditions") }}
            </NuxtLink>
          </div>
        </div>
      </div>
    </footer>
  </section>
</template>

<style>
.top-footer {
  border-radius: 40px;
  background: linear-gradient(180deg, #fde2e1 0%, #e2eaf3 100%);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.25);
}
.footer-link {
  @apply font-normal hover:text-primary-red focus:text-primary-red disabled:text-black/30 [&.active]:text-primary-red;
}
.footer-li {
  @apply flex justify-center md:justify-start font-normal text-base leading-7 text-[#222222];
}
</style>

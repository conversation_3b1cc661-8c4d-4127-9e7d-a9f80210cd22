<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAuthStore } from "~/stores/auth";

const route = useRoute();
const { isSmaller } = useBreakpoints();
const localePath = useLocalePath();
const { isLoggedIn } = storeToRefs(useAuthStore());

const isMobile = computed(() => isSmaller(1024));
const isMenuOpen = ref(false);

const iosPayment = computed(() => {
  return Boolean(route.query?.iosToken || route.query?.iosPayment);
});

const toggleDropdown = () => {
  isMenuOpen.value = !isMenuOpen.value;
};
const closeMenu = () => {
  if (isMobile.value) {
    isMenuOpen.value = false;
  }
  return;
};
const controlMenuInScreen = (windowWidth: any) => {
  if (windowWidth > 1023) {
    isMenuOpen.value = true;
  } else {
    isMenuOpen.value = false;
  }
};
defineExpose({
  closeMenu,
});
onMounted(() => {
  window.addEventListener("resize", (e) => {
    controlMenuInScreen(window.innerWidth);
  });

  controlMenuInScreen(window.innerWidth);
});
const isActivePage = (
  pages: String[],
  activeClass: String,
  inactiveClass: String
) => {
  const currentRouteName = (route.name as any).slice(0, -5);
  return pages.includes(currentRouteName) ? activeClass : inactiveClass;
};
</script>

<template>
  <section>
    <div class="bg-rgba(255, 255, 255, 1) notranslate">
      <nav
        class="relative flex w-full flex-wrap items-center justify-between text-neutral-500 shadow-lg"
        data-te-navbar-ref
      >
        <div
          class="custom-container flex w-full items-center justify-between flex-col lg:flex-row relative"
        >
          <div class="flex justify-between w-full lg:w-fit">
            <div @click="closeMenu">
              <NuxtLink :to="localePath('/')">
                <img
                  class="py-1 w-[93px] h-[60px] md:h-[65px]"
                  src="/images/sk-new-logo.svg"
                  alt="SK Mobile School logo"
                />
              </NuxtLink>
            </div>

            <button
              v-if="!iosPayment"
              class="block border-0 bg-transparent text-neutral-500 hover:no-underline hover:shadow-none focus:no-underline focus:shadow-none focus:outline-none focus:ring-0 dark:text-neutral-200 lg:hidden"
              type="button"
              @click.stop="toggleDropdown"
            >
              <span class="[&>svg]:w-7">
                <IconsHamburgerMenu />
              </span>
            </button>
          </div>

          <div
            v-if="isMenuOpen && !iosPayment"
            class="mobile-menu mt-2 flex-grow basis-[100%] items-center lg:mt-0 lg:flex lg:flex-wrap lg:basis-auto justify-end w-full lg:w-fit"
            id="navbarSupportedContent4"
          >
            <ul
              class="list-style-none ml-auto flex flex-col pl-0 lg:flex-row flex-wrap justify-end"
            >
              <li
                class="menu-item"
                :class="
                  isActivePage(['index'], 'border-primary-red', 'border-white')
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="isActivePage(['index'], 'text-primary-red', '')"
                  aria-current="page"
                  :to="localePath('/')"
                  >{{ $t("home") }}</NuxtLink
                >
              </li>
              <li
                class="menu-item"
                :class="
                  isActivePage(['blog'], 'border-primary-red', 'border-white')
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="isActivePage(['blog'], 'text-primary-red', '')"
                  aria-current="page"
                  :to="localePath('/blog')"
                  >Blog</NuxtLink
                >
              </li>
              <li
                class="menu-item"
                :class="
                  isActivePage(
                    ['success-story'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="
                    isActivePage(['success-story'], 'text-primary-red', '')
                  "
                  aria-current="page"
                  :to="localePath('/success-story')"
                  >{{ $t("success_Story") }}</NuxtLink
                >
              </li>
              <li
                class="menu-item"
                :class="
                  isActivePage(
                    ['firmware'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="isActivePage(['firmware'], 'text-primary-red', '')"
                  aria-current="page"
                  :to="localePath('/firmware')"
                  >Flash File/Tools</NuxtLink
                >
              </li>
              <li
                class="menu-item"
                :class="
                  isActivePage(
                    ['communication'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="
                    isActivePage(['communication'], 'text-primary-red', '')
                  "
                  aria-current="page"
                  :to="localePath('/communication')"
                  >{{ $t("communication") }}</NuxtLink
                >
              </li>
              <li
                class="menu-item"
                :class="
                  isActivePage(
                    ['online-services'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  class="nav-link-custom"
                  :class="
                    isActivePage(['online-services'], 'text-primary-red', '')
                  "
                  aria-current="page"
                  :to="localePath('/online-services')"
                  >{{ $t("online_services") }}</NuxtLink
                >
              </li>
              <li
                class="py-2 mx-2 self-center lg:border-b-4"
                :class="
                  isActivePage(
                    ['browse-course'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  @click="closeMenu"
                  type="button"
                  :to="localePath('/browse-course')"
                  class="animate-button red-button text-sm px-4"
                >
                  {{ $t("browse_course") }}
                </NuxtLink>
              </li>
              <li
                class="py-2 lg:ml-2 xl:ml-12 self-center lg:border-b-4"
                :class="
                  isActivePage(
                    ['auth-login', 'dashboard'],
                    'border-primary-red',
                    'border-white'
                  )
                "
              >
                <NuxtLink
                  v-if="!isLoggedIn"
                  class="red-button text-sm"
                  :to="localePath('/auth/login')"
                  @click="closeMenu"
                >
                  <span>{{ $t("login") }}</span>
                </NuxtLink>
                <NuxtLink
                  v-else
                  class="red-button text-sm"
                  :to="localePath('/dashboard')"
                  @click="closeMenu"
                >
                  <span> {{ $t("dashboard") }}</span>
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </section>
</template>

<style scoped>
.menu-item {
  @apply mx-2 py-4 self-center w-full lg:w-fit text-center whitespace-nowrap lg:border-b-4;
}
.dropdown-content {
  @apply text-lg bg-white;
  z-index: 1;
}
.dropdown-content li:hover {
  @apply bg-black text-white font-semibold;
}
@media screen and (min-width: 1024px) and (max-width: 1200px) {
  .menu-item {
    @apply mx-1;
  }
}
@media (max-width: 1023px) {
  .mobile-menu {
    @apply absolute top-[54px] md:top-[60px] right-0 z-20 bg-white shadow-xl pb-2;
  }
}
</style>

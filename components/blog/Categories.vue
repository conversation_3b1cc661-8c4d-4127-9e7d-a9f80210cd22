<script setup>
const { BLOG_CATEGORIES } = useUrls();
const localePath = useLocalePath();

const { data: categoriesRes } = useFetch(`${BLOG_CATEGORIES}?per_page=-1`);
const categories = computed(() => categoriesRes.value?.data);
</script>
<template>
  <div
    class="px-0.5 w-full pt-5 pb-8 xl:pb-12 bg-[#F8F8F8] rounded-md shadow-[2px_2px_6px_#65656527]"
  >
    <h3
      class="px-7 pb-5 xl:pb-[30px] text-xl xl:text-[28px] text-[#505050] font-bold"
    >
    {{ $t("categories") }}
    </h3>
    <div
      class="px-7 text-base xl:text-xl text-[#656565] max-h-[360px] xl:max-h-[440px] scroll overflow-y-auto"
    >
      <p
        v-for="(category, index) in categories"
        :key="category.id"
        class="flex items-center justify-between py-1.5 xl:py-2"
      >
        <NuxtLink
          :to="localePath(`/blog?category=${category.slug}`)"
          class="hover:text-[#ec1f27]"
          >{{ category.title }}</NuxtLink
        >
        <span>({{ category?.blogs_count ? category.blogs_count : 0 }})</span>
      </p>
    </div>
  </div>
</template>

<style scoped>
.scroll::-webkit-scrollbar {
  width: 6px;
}
</style>

<template>
  <svg
    width="106"
    height="106"
    viewBox="0 0 106 106"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="53" cy="53" r="53" class="animate"/>
    <path
      d="M39.7501 70.6668C39.3596 70.6668 38.9851 70.5117 38.709 70.2356C38.4329 69.9595 38.2778 69.5851 38.2778 69.1946V36.8057C38.2779 36.5499 38.3446 36.2985 38.4714 36.0763C38.5982 35.8541 38.7807 35.6688 39.0009 35.5386C39.2212 35.4084 39.4715 35.3379 39.7273 35.3339C39.9831 35.33 40.2355 35.3927 40.4597 35.516L69.9041 51.7105C70.1349 51.8376 70.3274 52.0243 70.4615 52.2511C70.5955 52.478 70.6663 52.7367 70.6663 53.0002C70.6663 53.2637 70.5955 53.5223 70.4615 53.7492C70.3274 53.976 70.1349 54.1627 69.9041 54.2898L40.4597 70.4843C40.2423 70.6039 39.9982 70.6667 39.7501 70.6668Z"
      fill="white"
    />
  </svg>
</template>
<style scoped>
.animate {
  animation: changeFillColor 2s infinite;
}
@keyframes changeFillColor {
  0% {
    fill: #000000;
  }
  33% {
    fill: #f7088c;
  }
  66% {
    fill: #ec1f27;
  }
  100% {
    fill: #000000;
  }
}
</style>

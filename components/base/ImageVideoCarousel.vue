<script setup>
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";
import { Carousel, Slide, Navigation, Pagination } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

const props = defineProps({
  slider: {
    type: Array,
    required: true,
  },
  carouselItemsClass: {
    type: String,
    required: false,
    default: "max-h-[440px]",
  },
  showPaginating: {
    type: Boolean,
    required: false,
    default: false,
  },
  showNavigation: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const selectedItem = ref(null);

const setItem = (id) => {
  if (selectedItem.value === id) {
    selectedItem.value = null;
  } else {
    selectedItem.value = id;
  }
};
const isSelected = (id) => {
  return selectedItem.value === id;
};
</script>

<template>
  <Carousel
    v-if="slider && slider.length > 0"
    class="consultation-carousel"
    :items-to-show="1"
    :transition="500"
    :autoplay="2000"
    :wrapAround="false"
    :pauseAutoplayOnHover="true"
    napAlign="center"
  >
    <Slide class="" v-for="slide in slider" :key="slide.id">
      <div class="w-full my-auto" :class="[carouselItemsClass]">
        <img
          v-if="slide.type === 'image'"
          class="object-cover w-full aspect-video"
          :src="slide.media_link || slide.media_url"
          alt="consultation"
        />
        <div v-if="slide.type === 'video'" class="bg-[#15151F]">
          <Transition name="page" mode="out-in">
            <div v-if="!isSelected(slide.id)" class="relative">
              <img
                class="w-full aspect-video z-[4]"
                :src="slide?.cover_image ? slide.cover_image : defaultThumb"
                alt=""
              />
              <div
                class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
              >
                <div
                  @click="setItem(slide.id)"
                  class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                >
                  <IconsPlay class="w-[18px] aspect-[3/4]" />
                </div>
              </div>
            </div>
            <iframe
              v-else
              class="w-full aspect-video"
              :src="`${
                slide?.media_link ? slide.media_link : slide?.media_url
              }?rel=0&autoplay=1`"
              frameborder="0"
              allowfullscreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            ></iframe>
          </Transition>
        </div>
      </div>
    </Slide>

    <template #addons>
      <Navigation v-if="showNavigation" />
      <Pagination v-if="showPaginating" />
    </template>
  </Carousel>
  <NoPageFound v-else minHeight="200" />
</template>

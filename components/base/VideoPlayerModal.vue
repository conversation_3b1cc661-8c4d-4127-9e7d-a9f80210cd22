<script setup>
import { storeToRefs } from "pinia";
import { useIndexStore } from "~/stores/index";

const { media } = storeToRefs(useIndexStore());
const { setMedia } = useIndexStore();
</script>

<template>
  <div
    v-if="media"
    class="bg-black/40 flex justify-center items-center fixed inset-0 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full z-[9999]"
  >
    <div class="absolute top-10 right-8" @click.stop="setMedia(null)">
      <ClientOnly>
        <fa
          class="w-8 h-8 cursor-pointer text-primary-red"
          :icon="['fas', 'times']"
        />
      </ClientOnly>
    </div>
    <div
      class="custom-container !w-full !max-w-[1640px] xl:!p-[200px] md:p-[24px] rounded-[32px] flex justify-center items-center overflow-hidden lg:px-[60px] px-0"
    >
      <iframe
        v-if="media.type === 'video'"
        class="w-full aspect-video rounded-[32px] bg-[#15151F]"
        :src="`${
          media?.media_link ? media.media_link : media?.media_url
        }?rel=0&autoplay=1&showinfo=0`"
        frameborder="0"
        allowfullscreen
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        @click.stop=""
      ></iframe>
      <img
        v-else
        class="w-full aspect-video rounded-[32px]"
        :src="media?.media_link ? media.media_link : media?.media_url"
        :alt="media.title"
        @click.stop=""
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  pageOverView: {
    type: Object,
    required: true,
  },
  groupButton: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div
    class="flex flex-col-reverse lg:flex-row items-center justify-between gap-10 xl:gap-[56px]"
  >
    <div
      class="w-full lg:w-1/2 lg:max-w-[564px] h-full text-center lg:text-left font-noto-sans-bengali"
    >
      <p class="text-sm lg:text-base font-medium pb-[14px]">
        {{ pageOverView.sub_title }}
      </p>
      <h1
        class="lg:max-w-[500px] pb-1 text-primary-red font-bold text-3xl lg:text-4xl xl:text-5xl leading-default"
      >
        {{ pageOverView.title }}
      </h1>
      <div
        class="text-base lg:text-xl text-center lg:text-start leading-6"
        v-html="pageOverView.content"
      ></div>

      <BaseGroupButton v-if="groupButton" class="mt-6" />
    </div>
    <div
      v-if="pageOverView?.media_link"
      class="w-full h-full xl:max-w-[650px] 2xl:max-w-[50%] rounded-[20px] overflow-hidden flex-grow"
    >
      <BaseEmbadedVideoPlayer
        v-if="pageOverView.type === 'video'"
        playerId="homeHeroPlayer"
        :videoTitle="pageOverView.title"
        :videoUrl="pageOverView.media_link"
        :coverImage="pageOverView.cover_image"
      />
      <img
        v-else
        :src="pageOverView.media_link"
        class="object-cover w-full aspect-video rounded-[20px]"
        :alt="pageOverView.title"
      />
    </div>
  </div>
</template>
<style scoped>
.leading-default {
  line-height: normal;
}
</style>

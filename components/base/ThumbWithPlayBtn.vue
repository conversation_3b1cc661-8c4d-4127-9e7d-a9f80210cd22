<script setup>
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

const props = defineProps({
  coverImage: {
    type: String,
    default: "",
  },
  altText: {
    type: String,
    default: "Video Title",
  },
  playBtnClass: {
    type: String,
    default: "w-14 lg:w-[60px]",
  },
  thumbClass: {
    type: String,
    default: "w-full aspect-video rounded-[20px]",
  },
  wrapperClass: {
    type: String,
    default: "w-full rounded-[20px]",
  },
});
</script>

<template>
  <div class="w-full bg-[#15151F] relative" :class="wrapperClass">
    <img
      class="z-[4]"
      :class="thumbClass"
      :src="coverImage ? coverImage : defaultThumb"
      :alt="altText"
    />
    <div class="absolute inset-0 m-auto z-[5] flex justify-center items-center">
      <IconsAnimatedPlayBtn
        @click="$emit('toggleMedia')"
        class="aspect-square cursor-pointer rounded-full"
        :class="playBtnClass"
      />
    </div>
  </div>
</template>

<script setup>
defineProps({
  wrapperClass: {
    type: String,
    default: "flex-wrap lg:flex-nowrap",
  },
});
const localePath = useLocalePath();
</script>

<template>
  <div
    class="flex justify-center lg:justify-start gap-5 whitespace-nowrap"
    :class="wrapperClass"
  >
    <NuxtLink
      :to="localePath('/browse-course')"
      class="red-button animate-button w-[178px]"
    >
      <span>{{ $t("browse_course") }}</span>
    </NuxtLink>
    <NuxtLink :to="localePath('/firmware')" class="red-button w-[178px]">
      <span>Flash File/Tools</span>
    </NuxtLink>
    <NuxtLink
      :to="localePath('/demo-class')"
      class="red-button bg-white text-black border-2 border-black w-[178px]"
    >
      <span>{{ $t("view_demo_class") }}</span>
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  error: {
    type: String,
    default: "",
  },
});

const { error: errorMessages } = toRefs(props);
const emit = defineEmits(["phone", "resetError"]);
const { t } = useI18n();

const phoneNumberInput = ref(null);
const phoneNumber = ref("");
const finalPhone = ref("");
const error = ref(false);
const errorMessage = ref("");

const dropdownOptions = {
  showDialCodeInList: true,
  showDialCodeInSelection: true,
  showFlags: true,
  showSearchBox: true,
};
const inputOptions = {
  maxlength: 20,
  placeholder: t("phone_number"),
  styleClasses: "telInput",
};
const generateInput = () => {
  if (phoneNumber.value.includes("+")) {
    const extracted = phoneNumber.value.substring(
      0,
      phoneNumber.value.indexOf(" ")
    );
    const restOfNumber = phoneNumber.value.substring(
      phoneNumber.value.indexOf(" ") + 1
    );
    phoneNumber.value = restOfNumber;
    finalPhone.value = extracted + " " + restOfNumber;
  }
  emit("phone", {
    error: error.value,
    nationalNumber: phoneNumber.value,
    formated: finalPhone.value,
    phone: finalPhone.value.replace(/\s/g, ""),
  });
};
const formBlur = () => {
  if (phoneNumber.value === "") {
    error.value = true;
    errorMessage.value = t("field_required");
    emit("phone", {
      error: error.value,
      nationalNumber: phoneNumber.value,
      formated: finalPhone.value,
    });
  }
};
const formInput = () => {
  if (phoneNumber.value === "") {
    error.value = true;
    errorMessage.value = t("field_required");
  } else {
    generateInput();
  }
};

const getFinalNumber = () => {
  setTimeout(() => {
    generateInput();
  });
};

const formValidation = (data: any) => {
  if (data.valid !== "undefined") {
    if (data.valid) {
      emit("resetError");
      error.value = false;
      errorMessage.value = "";
      generateInput();
    } else if (!data.valid && phoneNumber.value !== "") {
      error.value = true;
      errorMessage.value = t("invalid_phone");
      emit("phone", {
        error: error.value,
        nationalNumber: phoneNumber.value,
        formated: finalPhone.value,
      });
    } else if (!data.valid && phoneNumber.value === "") {
      emit("resetError");
      error.value = false;
      emit("phone", {
        error: error.value,
        nationalNumber: phoneNumber.value,
        formated: finalPhone.value,
      });
    } else {
      emit("resetError");
      error.value = false;
      errorMessage.value = "";
      emit("phone", {
        error: error.value,
        nationalNumber: phoneNumber.value,
        formated: finalPhone.value,
      });
    }
  } else {
    emit("resetError");
    error.value = false;
    errorMessage.value = "";
    emit("phone", {
      error: error.value,
      nationalNumber: phoneNumber.value,
      formated: finalPhone.value,
    });
  }
};

watch(errorMessages, (newValue) => {
  if (newValue) {
    error.value = true;
    errorMessage.value = newValue;
  } else {
    error.value = false;
    errorMessage.value = "";
  }
  formInput();
});
</script>
<template>
  <div class="phone-number-input">
    <vue-tel-input
      ref="phoneNumberInput"
      v-model="phoneNumber"
      styleClasses="tel-wrapper"
      :validCharactersOnly="true"
      :autoFormat="true"
      defaultCountry="bd"
      :dropdown-options="dropdownOptions"
      :inputOptions="inputOptions"
      mode="international"
      @blur="formBlur"
      @on-input="formInput"
      @validate="formValidation"
      @country-changed="getFinalNumber"
    >
    </vue-tel-input>
    <div v-if="error" class="error text-sm text-left text-red-500 mt-1">
      {{ errorMessage }}
    </div>
  </div>
</template>

<style>
.tel-wrapper {
  @apply w-full !border !border-black !rounded-[10px] text-lg font-semibold !shadow-none;
}
.telInput {
  @apply !rounded-[10px];
}
.tel-wrapper .vti__dropdown {
  @apply py-2 pl-6 rounded-l-[10px];
}
.vti__dropdown-list.below {
  top: 44px;
}
</style>

<script setup>
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";
import { Carousel, Slide, Pagination } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

const props = defineProps({
  slider: {
    type: Array,
    required: true,
  },
});

const settings = {
  itemsToShow: 1,
  snapAlign: "center",
  modelValue: props.slider.length === 3 ? 1 : 0,
  autoplay: 5000,
  wrapAround: false,
  pauseAutoplayOnHover: true,
};
const breakpoints = {
  630: {
    itemsToShow: 2,
    snapAlign: "center",
  },
  960: {
    itemsToShow:
      props.slider.length <= 2 ? 1 : props.slider.length >= 3 ? 3 : "",
    snapAlign: "center",
  },
  // 1280: {
  //   itemsToShow:
  //     props.slider.length === 4 || props.slider.length > 4
  //       ? 4
  //       : props.slider.length,
  //   snapAlign: "center",
  // },
};
const selectedItem = ref(null);

const setItem = (id) => {
  if (selectedItem.value === id) {
    selectedItem.value = null;
  } else {
    selectedItem.value = id;
  }
};
const isSelected = (id) => {
  return selectedItem.value === id;
};
</script>

<template>
  <Carousel
    v-if="props.slider && props.slider.length > 0"
    v-bind="settings"
    :breakpoints="breakpoints"
  >
    <Slide v-for="item in props.slider" :key="item.id">
      <div>
        <div class="carousel__item w-[310px] aspect-video mb-4">
          <img
            v-if="item.type === 'image'"
            class="object-cover object-top w-[305px] h-[200px]"
            :src="item.media_url"
            alt="SK Mobile School slider image"
          />
          <div v-if="item.type === 'video'" class="bg-[#15151F]">
            <Transition name="page" mode="out-in">
              <div v-if="!isSelected(item.id)" class="relative">
                <img
                  class="w-full aspect-video z-[4]"
                  :src="item?.cover_image ? item.cover_image : defaultThumb"
                  alt=""
                />
                <div
                  class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
                >
                  <div
                    @click="setItem(item.id)"
                    class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                  >
                    <IconsPlay class="w-[18px] aspect-[3/4]" />
                  </div>
                </div>
              </div>
              <iframe
                v-else
                class="w-full aspect-video"
                :src="`${item.media_url}?rel=0&autoplay=1`"
                frameborder="0"
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              ></iframe>
            </Transition>
          </div>
        </div>
        <p
          v-if="item.title"
          class="text-2xl font-semibold"
          :class="item.title.length > 20 ? 'has-tooltip' : ''"
        >
          {{ item.title.slice(0, 20)
          }}<span v-if="item.title.length > 20">...</span>
          <span class="tooltip line-clamp-3">{{ item.title }}</span>
        </p>
      </div>
    </Slide>

    <template #addons>
      <div class="mt-7">
        <Pagination />
      </div>
    </template>
  </Carousel>
  <NoPageFound v-else minHeight="220" />
</template>

<style scoped>
.has-tooltip {
  @apply relative;
}
.tooltip {
  @apply absolute bg-primary-red text-white z-[100] left-8 -top-[56px] lg:-top-24 text-left invisible p-1.5 px-4 rounded-xl  shadow-lg w-full min-w-[340px] whitespace-normal break-words;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
</style>

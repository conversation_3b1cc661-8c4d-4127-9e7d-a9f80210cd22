<script setup>
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

const props = defineProps({
  playerId: {
    type: String,
    default: "playerId",
  },
  videoUrl: {
    type: String,
    default: "",
  },
  coverImage: {
    type: String,
    default: "",
  },
  isPlaying: {
    type: Boolean,
    default: false,
  },
  videoTitle: {
    type: String,
    default: "Video Title",
  },
  videoClass: {
    type: String,
    default: "w-full aspect-video rounded-[20px]",
  },
  playBtnClass: {
    type: String,
    default: "w-14 lg:w-[60px]",
  },
});

const { isPlaying } = toRefs(props);
const emit = defineEmits(["play"]);

const videoIsPlaying = ref(false);
const targetPlayer = ref(null);
const targetIsVisible = useElementVisibility(targetPlayer);

const playVideo = () => {
  videoIsPlaying.value = !videoIsPlaying.value;
  emit("play", videoIsPlaying.value);
};

watch(targetIsVisible, (newValue) => {
  if (!newValue) {
    videoIsPlaying.value = false;
  }
});

watch(isPlaying, (newValue) => {
  if (newValue) {
    videoIsPlaying.value = true;
  } else {
    videoIsPlaying.value = false;
  }
  emit("play", videoIsPlaying.value);
});
</script>

<template>
  <div
    :id="playerId"
    ref="targetPlayer"
    class="bg-[#15151F]"
    :class="videoClass"
  >
    <Transition name="page" mode="out-in">
      <div v-if="!videoIsPlaying" class="w-full relative">
        <img
          class="z-[4]"
          :class="videoClass"
          :src="coverImage ? coverImage : defaultThumb"
          :alt="videoTitle"
        />
        <div
          class="absolute inset-0 m-auto z-[5] flex justify-center items-center"
        >
          <IconsAnimatedPlayBtn
            @click="playVideo"
            class="aspect-square cursor-pointer rounded-full"
            :class="playBtnClass"
          />
        </div>
      </div>
      <iframe
        v-else
        :class="videoClass"
        :src="`${videoUrl}?rel=0&autoplay=1&showinfo=0`"
        frameborder="0"
        allowfullscreen
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      ></iframe>
    </Transition>
  </div>
</template>

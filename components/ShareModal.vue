<script setup>
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  courseUrl: {
    type: String,
    default: "/",
  },
});
const copySuccess = ref(false);
const copyToClipboard = async () => {
  try {
    copySuccess.value = false;
    await navigator.clipboard.writeText(props.courseUrl);
    copySuccess.value = true;
    setTimeout(() => {
      copySuccess.value = false;
    }, 1000);
  } catch (err) {
    copySuccess.value = false;
  }
};
</script>

<template>
  <Transition name="modal">
    <div
      v-if="showModal"
      class="modal-mask fixed p-4 inset-0 flex items-center justify-center z-[999999]"
    >
      <div
        class="fixed inset-0 bg-gray-600 opacity-50"
        @click="$emit('closeModal')"
      >
        <div class="modal-mask absolute inset-0 opacity-75"></div>
      </div>

      <div
        class="modal-container min-h-[260px] w-full md:min-w-[460px] sm:max-w-[80%] lg:w-[60%] 2xl:w-[40%]"
      >
        <IconsCross
          class="w-4 text-primary-red absolute top-4 right-4 m-2 cursor-pointer"
          @click="$emit('closeModal')"
        />
        <div class="pt-4 mx-6 border-b-2 border-gray-400">
          <h4 class="text-2xl text-primary-red pb-2 font-semibold">
            {{ $t("share_course") }}
          </h4>
        </div>
        <div
          class="px-6 pt-10 pb-12 max-h-[calc(100vh-160px)] overflow-y-auto text-black"
        >
          <p class="text-center">
            {{ $t("share_text") }}
          </p>
          <div class="pt-12">
            <p class="text-xl font-bold pb-3">{{ $t("copy_link") }}</p>
            <div
              class="flex items-center justify-between border border-gray-500 rounded-xl"
            >
              <NuxtLink
                class="p-3 text-gray-400 hover:text-black"
                :to="courseUrl"
                target="_blank"
              >
                <span class="line-clamp-1 break-all">
                  {{ courseUrl }}
                </span>
              </NuxtLink>
              <div class="px-7 py-3 border-l border-gray-500 relative">
                <div :title="$t('copy')">
                  <IconsCopy
                    @click="copyToClipboard"
                    class="w-5 aspect-video cursor-pointer"
                  />
                </div>
                <span
                  v-if="copySuccess"
                  class="text-sm absolute top-[52px] left-0 right-0 mx-auto text-center whitespace-nowrap font-semibold text-gray-600"
                  >{{ $t("link_copied") }}</span
                >
              </div>
            </div>
          </div>
          <div class="pt-7">
            <p class="text-xl font-bold pb-3">{{ $t("share_to_media") }}</p>
            <div
              class="flex gap-5 items-center justify-start flex-wrap rounded-xl"
            >
              <NuxtLink
                :to="`https://www.facebook.com/share.php?u=${courseUrl}`"
                target="_blank"
              >
                <img
                  src="/icons/facebook.png"
                  class="h-10 aspect-[5/2] object-contain"
                  alt="courseDetails title"
                />
              </NuxtLink>
              <NuxtLink
                :to="`http://twitter.com/share?&url=${courseUrl}`"
                target="_blank"
              >
                <img
                  src="/icons/twitter.png"
                  class="h-10 aspect-[5/2] object-contain"
                  alt="courseDetails title"
                />
              </NuxtLink>
              <NuxtLink
                :to="`https://www.linkedin.com/sharing/share-offsite/?url=${courseUrl}`"
                target="_blank"
              >
                <img
                  src="/icons/linkedin.png"
                  class="h-10 aspect-[117/40] object-contain"
                  alt="courseDetails title"
                />
              </NuxtLink>
              <NuxtLink
                :to="`instagram-stories://share?source=${courseUrl}`"
                target="_blank"
                class="hidden"
              >
                <img
                  src="/icons/instagram.png"
                  class="h-10 aspect-[129/40] object-contain"
                  alt="courseDetails title"
                />
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>
<style scoped>
.modal-container {
  @apply bg-white rounded-lg overflow-hidden shadow-xl relative border-[#00000048];
  transition: all 0.3s ease;
}
.modal-mask {
  transition: opacity 0.3s ease;
}
.modal-enter-from .modal-mask,
.modal-leave-to .modal-mask {
  opacity: 0;
}
.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  opacity: 0;
  transform: scale(1.1);
  -webkit-transform: scale(1.1);
}
</style>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { ErrorMessage, Field, Form } from "vee-validate";
import { useAuthStore } from "~/stores/auth";
import { useCartDataStore } from "~/stores/cartData";
import { useSubscriptionPackageStore } from "~/stores/subscriptionPackage";

const props = defineProps({
  banks: {
    type: Object,
    required: true,
  },
  totalPrice: {
    type: String,
    default: 0,
  },
  currentBillingComp: {
    type: String,
    default: "",
  },
});

const { banks, totalPrice } = toRefs(props);
const emit = defineEmits(["submitPayment"]);

const { setAllCartData, setCartDataForApi } = useCartDataStore();
const { setSubscriptionPackageId } = useSubscriptionPackageStore();
const { allCartData, subscriptionDataForApi } = storeToRefs(useCartDataStore());
const { subscriptionPackageId } = storeToRefs(useSubscriptionPackageStore());
const isIOSPayment = computed(() => (route.query?.iosPayment ? true : false));
const { setUser } = useAuthStore();
const { ORDER } = useUrls();
const { $toast } = useNuxtApp();
const { setRedirectUrl } = useAuth();
const { isRequired, validateNumeric } = useValidation();
const route = useRoute();
const router = useRouter();
const localePath = useLocalePath();
const { t } = useI18n();

const tokenCookie = useCookie("token");
const deviceId = useCookie("deviceId");
const isLoading = ref(false);
const currentPaymentType = ref("mobile");
const currentPaymentMethod = ref("");
const focusValue = ref("");
const payment = ref({
  accountNumber: "",
  transactionNumber: "",
  paymentMethod: "",
  focusValue: "",
});

const setPaymentMethodType = (method: string) => {
  currentPaymentType.value = method;
  if (currentPaymentType.value === "mobile") {
    if (banks.value.mobileBanking.length > 0) {
      setPaymentMethod(banks.value.mobileBanking[0].bank_name);
    }
  } else {
    if (banks.value.mobileBanking.length > 0) {
      setPaymentMethod(banks.value.onlineBanking[0].bank_name);
    }
  }
};

const setPaymentMethod = (method: string) => {
  currentPaymentMethod.value = method;
};

const setFocusValue = (value: any) => {
  const el = document.getElementById(value);
  if (el) {
    el.focus();
  }
  focusValue.value = value;
};

const submitPaymentForm = async () => {
  isLoading.value = true;
  window.removeEventListener("beforeunload", beforeunload);
  if (
    !tokenCookie.value ||
    tokenCookie.value === "" ||
    tokenCookie.value === undefined
  ) {
    setRedirectUrl(route.fullPath);
    router.push(localePath("/auth/login"));
    $toast("clear");
    $toast("error", {
      message: t("messages.login_first"),
      className: "toasted-bg-alert",
    });
  } else {
    if (allCartData.value.length > 0 && props.currentBillingComp === "video") {
      const orderItemDetails = [
        {
          item_id: allCartData.value[0]?.item?.id,
          subscription_id: allCartData.value[0]?.subscription?.id,
        },
      ];
      try {
        const { data } = await useFetch<any>(ORDER, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
            "x-device-id": deviceId.value || "",
          },
          body: {
            code: allCartData.value.code ? allCartData.value.code : "",
            type: "video",
            account_number: payment.value.accountNumber,
            transaction_id: payment.value.transactionNumber,
            items: orderItemDetails,
            payment_method: currentPaymentMethod.value,
          },
        });
        if (!data.value.error) {
          router.push({
            query: {
              ...route.query,
              payment: "done",
            },
          });
          emit("submitPayment");
          $toast("clear");
          $toast("success", {
            message: data.value.message,
            className: "toasted-bg-success",
          });
          // window.open(data.value.url, "_self");
          setTimeout(() => {
            setAllCartData([]);
            setCartDataForApi([]);
            if (isIOSPayment.value) {
              const tokenCookieAgain = useCookie("token");
              tokenCookieAgain.value = "";
              setUser(null);
            }
          }, 400);
        }
      } catch (error: any) {
        $toast("clear");
        $toast("error", {
          message: error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
      } finally {
        isLoading.value = false;
        window.scrollTo(0, 0);
        window.addEventListener("beforeunload", beforeunload);
      }
    } else if (props.currentBillingComp === "file") {
      const fileDetails = subscriptionDataForApi.value.downloadFileDetails;
      setRedirectUrl(`/download/${fileDetails.slug}`);
      try {
        const { data } = await useFetch<any>(ORDER, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
            "x-device-id": deviceId.value || "",
          },
          body: {
            code: "",
            type:
              subscriptionDataForApi.value.type === "file"
                ? subscriptionDataForApi.value.type
                : subscriptionPackageId.value[0]?.subscriptionFrom,
            payment_method: currentPaymentMethod.value,
            account_number: payment.value.accountNumber,
            transaction_id: payment.value.transactionNumber,
            items:
              subscriptionDataForApi.value.type === "file"
                ? [{ item_id: fileDetails.id }]
                : [
                    {
                      subscription_id:
                        subscriptionPackageId.value[0].subscription_id,
                    },
                  ],
          },
        });

        if (!data.value.error) {
          router.push({
            query: {
              ...route.query,
              payment: "done",
            },
          });
          emit("submitPayment");
          $toast("clear");
          $toast("success", {
            message: data.value.message,
            className: "toasted-bg-success",
          });
          // window.open(data.value.url, "_self");
          setTimeout(() => {
            setAllCartData([]);
            setCartDataForApi([]);
            setSubscriptionPackageId([]);
            if (isIOSPayment.value) {
              const tokenCookieAgain = useCookie("token");
              tokenCookieAgain.value = "";
              setUser(null);
            }
          }, 400);
        }
      } catch (error: any) {
        $toast("clear");
        $toast("error", {
          message: error.response?.message,
          className: "toasted-bg-alert",
        });
      } finally {
        isLoading.value = false;
        window.scrollTo(0, 0);
        window.addEventListener("beforeunload", beforeunload);
      }
    }
  }
};

const beforeunload = (e: any) => {
  e.preventDefault();
  e.returnValue = "Data will be lost if you reload the page.";
};

watch(currentPaymentMethod, (newValue) => {
  payment.value.accountNumber = "";
  payment.value.transactionNumber = "";
});

onMounted(() => {
  setPaymentMethodType("mobile");
  window.addEventListener("beforeunload", beforeunload);
});
onUnmounted(() => {
  window.removeEventListener("beforeunload", beforeunload);
});
</script>

<template>
  <div class="w-full max-w-[644px] mx-auto px-5">
    <div class="space-x-12 border-b border-[#E3E3E3] inline-block">
      <button
        class="px-2 pb-2 text-base md:px-5 md:pb-3.5 md:text-xl border-b-4"
        :class="currentPaymentType === 'mobile' ? 'active' : 'inactive'"
        @click="setPaymentMethodType('mobile')"
      >
        {{ $t("Mobile_Banking") }}
      </button>
      <button
        class="px-2 pb-2 text-base md:px-5 md:pb-3.5 md:text-xl border-b-4"
        :class="currentPaymentType === 'online' ? 'active' : 'inactive'"
        @click="setPaymentMethodType('online')"
      >
        {{ $t("Online_Banking") }}
      </button>
    </div>
    <Transition name="page" mode="out-in">
      <div
        v-if="currentPaymentType === 'mobile'"
        class="payment_box p-[24px] md:p-[32px] xl:px-[62px] xl:py-[62px] mt-[30px]"
      >
        <Form
          @submit="submitPaymentForm"
          @keypress.enter="submitPaymentForm"
          v-slot="{ meta }"
        >
          <div
            v-for="(item, index) in banks.mobileBanking"
            :key="index"
            class="flex flex-col py-[30px] border-[ #E3E3E3]"
            :class="
              index === banks.mobileBanking.length - 1
                ? 'border-none'
                : 'border-b'
            "
          >
            <div class="flex items-center space-x-5">
              <input
                type="radio"
                :id="`mobile_${item.id}`"
                name="payment"
                :value="`${item.bank_name}`"
                v-model="currentPaymentMethod"
              />
              <label
                :for="`mobile_${item.id}`"
                class="cursor-pointer flex items-center space-x-3.5"
                ><img :src="item.logo" class="max-h-[40px] h-10" /><span
                  class="text-xl font-medium text-gray-800 capitalize"
                  >{{ item.bank_name }}</span
                >
              </label>
            </div>
            <div v-if="currentPaymentMethod === item.bank_name">
              <p class="text-gray-800 text-base pt-6">
                {{ $t("Please_complete_the") }}
                <span class="font-bold text-orange-primary">{{
                  $t("SEND_MONEY")
                }}</span>
                {{ $t("from_your_account_first_then_fill_out_the_form_below") }}
              </p>
              <p class="pt-5 font-medium">
                {{ item.bank_name }} :
                <span class="font-normal">{{ item.account_number }}</span>
              </p>
              <p v-if="item.description" class="pt-5">{{ item.description }}</p>
              <div
                class="flex w-full flex-col md:flex-row pt-9 space-y-8 md:space-y-0 md:space-x-8"
              >
                <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                  <div
                    class="w-full relative"
                    @focusin="setFocusValue(`mobileAccountNo_${item.id}`)"
                    @focusout="setFocusValue('')"
                  >
                    <label
                      :for="`mobileAccountNo_${item.id}`"
                      tabindex="0"
                      class="outline-none absolute cursor-text transform left-4 transition-all duration-300 ease-in-out"
                      :class="
                        !payment.accountNumber &&
                        focusValue !== `mobileAccountNo_${item.id}`
                          ? 'text-base top-1/2 -translate-y-1/2 text-[#3131314D]'
                          : 'text-sm top-[-8px] bg-white '
                      "
                      >{{ $t("Your_Account_Number") }}
                    </label>
                    <Field
                      v-model="payment.accountNumber"
                      :id="`mobileAccountNo_${item.id}`"
                      :name="`mobileAccountNo_${item.id}`"
                      class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none w-full font-medium"
                      type="text"
                      :rules="validateNumeric"
                      :placeholder="
                        !payment.accountNumber &&
                        focusValue !== `mobileAccountNo_${item.id}`
                          ? ''
                          : '018XXXXXX300 '
                      "
                    />
                  </div>
                  <ErrorMessage
                    class="error-message"
                    :name="`mobileAccountNo_${item.id}`"
                  />
                </div>
                <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                  <div
                    class="w-full relative"
                    @focusin="setFocusValue(`mobileTransactionNo_${item.id}`)"
                    @focusout="setFocusValue('')"
                  >
                    <label
                      :for="`mobileTransactionNo_${item.id}`"
                      tabindex="0"
                      class="outline-none absolute cursor-text transform left-4 transition-all duration-300 ease-in-out"
                      :class="
                        !payment.transactionNumber &&
                        focusValue !== `mobileTransactionNo_${item.id}`
                          ? 'text-base top-1/2 -translate-y-1/2 text-[#3131314D]'
                          : 'text-sm top-[-8px] bg-white '
                      "
                      >{{ $t("Transaction_ID") }}
                    </label>
                    <Field
                      v-model="payment.transactionNumber"
                      :id="`mobileTransactionNo_${item.id}`"
                      :name="`mobileTransactionNo_${item.id}`"
                      class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none w-full font-medium"
                      type="text"
                      :rules="isRequired"
                      :placeholder="
                        !payment.transactionNumber &&
                        focusValue !== `mobileTransactionNo_${item.id}`
                          ? ''
                          : '72W5QMCJ '
                      "
                    />
                  </div>
                  <ErrorMessage
                    class="error-message"
                    :name="`mobileTransactionNo_${item.id}`"
                  />
                </div>
              </div>
            </div>
          </div>

          <button
            class="mt-10 w-full h-[60px] flex justify-between items-center rounded-md px-5 md:px-[30px] text-xl font-bold text-white"
            :class="
              !meta.valid || isLoading ? 'bg-[#E3E3E3]' : 'bg-primary-red'
            "
            type="submit"
            :disabled="!meta.valid || isLoading"
          >
            <span>{{ $t("Place_Order") }}</span>
            <span>৳ {{ totalPrice }}</span>
          </button>
        </Form>
      </div>
      <div
        v-else-if="currentPaymentType === 'online'"
        class="payment_box p-[24px] md:p-[32px] xl:px-[62px] xl:py-[62px] mt-[30px]"
      >
        <Form
          @submit="submitPaymentForm"
          @keypress.enter="submitPaymentForm"
          v-slot="{ meta }"
        >
          <div
            v-for="(item, index) in banks.onlineBanking"
            :key="index"
            class="flex flex-col py-[30px] border-[ #E3E3E3]"
            :class="
              index === banks.onlineBanking.length - 1
                ? 'border-none'
                : 'border-b'
            "
          >
            <div class="flex items-center space-x-5">
              <input
                type="radio"
                :id="`online_${item.id}`"
                name="payment"
                :value="`${item.bank_name}`"
                v-model="currentPaymentMethod"
              />
              <label
                :for="`online_${item.id}`"
                class="cursor-pointer flex items-center space-x-3.5"
                ><img :src="item.logo" class="max-h-[40px] h-10" /><span
                  class="text-xl font-medium text-gray-800 capitalize"
                  >{{ item.bank_name }}</span
                >
              </label>
            </div>
            <div v-if="currentPaymentMethod === item.bank_name">
              <div class="text-gray-800 text-base font-normal space-y-4">
                <p class="font-normal pt-6">
                  {{
                    $t(
                      "Please_complete_the_money_transfer_from_your_account_first_then_fill_out_the_form_below"
                    )
                  }}
                </p>
                <p class="pt-1 text-lg font-bold">
                  {{ $t("Account_Information") }}
                </p>
                <ul class="space-y-4">
                  <li>
                    <span class="font-medium pr-5"
                      >{{ $t("Account_Name") }}:</span
                    >{{ item.account_name }}
                  </li>
                  <li>
                    <span class="font-medium pr-5">{{ $t("Account_No") }}:</span
                    >{{ item.account_number }}
                  </li>
                  <li>
                    <span class="font-medium pr-5">{{ $t("Branch") }}:</span
                    >{{ item.branch.toUpperCase() }}
                  </li>
                  <li>
                    <span class="font-medium pr-5">{{ $t("District") }}:</span
                    >{{ item.district.toUpperCase() }}
                  </li>
                  <li>
                    <span class="font-medium pr-5">{{ $t("Routing_No") }}:</span
                    >{{ item.routing }}
                  </li>
                </ul>
              </div>
              <div
                class="flex w-full flex-col md:flex-row pt-9 space-y-8 md:space-y-0 md:space-x-8"
              >
                <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                  <div
                    class="w-full relative"
                    @focusin="setFocusValue(`onlineAccountNo_${item.id}`)"
                    @focusout="setFocusValue('')"
                  >
                    <label
                      :for="`onlineAccountNo_${item.id}`"
                      tabindex="0"
                      class="outline-none absolute cursor-text transform left-4 transition-all duration-300 ease-in-out"
                      :class="
                        !payment.accountNumber &&
                        focusValue !== `onlineAccountNo_${item.id}`
                          ? 'text-base top-1/2 -translate-y-1/2 text-[#3131314D]'
                          : 'text-sm top-[-8px] bg-white'
                      "
                      >{{ $t("Your_Account_Number") }}
                    </label>
                    <Field
                      v-model="payment.accountNumber"
                      :id="`onlineAccountNo_${item.id}`"
                      :name="`onlineAccountNo_${item.id}`"
                      class="border border-[#79747E] rounded-md px-3 py-3 outline-none w-full font-medium"
                      type="text"
                      :rules="validateNumeric"
                      :placeholder="
                        !payment.accountNumber &&
                        focusValue !== `onlineAccountNo_${item.id}`
                          ? ''
                          : '********* '
                      "
                    />
                  </div>
                  <ErrorMessage
                    class="error-message"
                    :name="`onlineAccountNo_${item.id}`"
                  />
                </div>
                <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                  <div
                    class="w-full relative"
                    @focusin="setFocusValue(`onlineTransactionNo_${item.id}`)"
                    @focusout="setFocusValue('')"
                  >
                    <label
                      :for="`onlineTransactionNo_${item.id}`"
                      tabindex="0"
                      class="outline-none absolute cursor-text transform left-4 transition-all duration-300 ease-in-out"
                      :class="
                        !payment.transactionNumber &&
                        focusValue !== `onlineTransactionNo_${item.id}`
                          ? 'text-base top-1/2 -translate-y-1/2 text-[#3131314D]'
                          : 'text-sm top-[-8px] bg-white '
                      "
                      >{{ $t("Transaction_ID") }}
                    </label>
                    <Field
                      v-model="payment.transactionNumber"
                      :id="`onlineTransactionNo_${item.id}`"
                      :name="`onlineTransactionNo_${item.id}`"
                      class="border border-[#79747E] rounded-md px-3 py-3 outline-none w-full font-medium"
                      type="text"
                      :rules="isRequired"
                      :placeholder="
                        !payment.transactionNumber &&
                        focusValue !== `onlineTransactionNo_${item.id}`
                          ? ''
                          : '72W5QMCJ '
                      "
                    />
                  </div>
                  <ErrorMessage
                    class="error-message"
                    :name="`onlineTransactionNo_${item.id}`"
                  />
                </div>
              </div>
            </div>
          </div>

          <button
            class="mt-10 w-full h-[60px] flex justify-between items-center rounded-md px-5 md:px-[30px] text-xl font-bold text-white"
            :class="
              !meta.valid || isLoading ? 'bg-[#E3E3E3]' : 'bg-primary-red'
            "
            type="submit"
            :disabled="!meta.valid || isLoading"
          >
            <span>{{ $t("Place_Order") }}</span>
            <span>৳ {{ totalPrice }}</span>
          </button>
        </Form>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.payment_box {
  border-radius: 8px;
  border: 0.5px solid #eff0f6;
  background: #fff;
  box-shadow: 0px 98px 66px 0px rgba(255, 110, 31, 0.02),
    0px 1px 104px 0px rgba(255, 110, 31, 0.04),
    0px 54px 54px 0px rgba(255, 110, 31, 0.02);
}
.active {
  @apply font-bold text-primary-red border-primary-red;
}
.inactive {
  @apply font-normal text-gray-600 border-transparent;
}
input[type="radio"] {
  accent-color: #e74c3c;
  width: 24px;
  height: 24px;
}
</style>

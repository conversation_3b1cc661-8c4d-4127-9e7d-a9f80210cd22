<script setup lang="ts">
import { Form, Field, ErrorMessage } from "vee-validate";
import { useDashboardStore } from "~/stores/dashboard";
import { useAuthStore } from "~/stores/auth";
import FingerprintJS from "@fingerprintjs/fingerprintjs";

const { LOGIN, CHECH_USER_EXISTS, VERIFY_CODE, REGISTER_USER } = useUrls();
const { validateEmail, validatePassword, isRequired } = useValidation();
const { setAllNotifications } = useDashboardStore();
const { setAuthCookies, tokenCookie } = useAuth();
const { setUser } = useAuthStore();
const nuxtApp = useNuxtApp();
const config = useRuntimeConfig();
const router = useRouter();
const redirectUrl = useCookie("redirectUrl");
const deviceIdCookie = useCookie("deviceId");
const localePath = useLocalePath();
const { joinToChannel } = useNotification();
const { t } = useI18n();
const emit = defineEmits(["loggedInSuccess", "signupSuccess"]);

const rememberMeCookie: any = useCookie("rememberMe", {
  watch: true,
});
const userEmail = ref(
  rememberMeCookie.value ? rememberMeCookie.value.email : ""
);
const userPass = ref(
  rememberMeCookie.value ? rememberMeCookie.value.password : ""
);
const showPassword = ref(false);
const deviceId = ref("");

const generateDeviceFingerprint = async () => {
  const fpPromise = FingerprintJS.load();
  const fp = await fpPromise;
  const result = await fp.get();
  deviceId.value = result.visitorId;
};

const recaptchaRef = ref<any>(null);
const loading = ref(false);
if (process.client) {
  nuxtApp.$toast("clear");
}
const handleLogin = async () => {
  loading.value = true;
  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "LOGIN",
      }
    );
    if (captchaToken) {
      try {
        const { token, user, message }: any = await $fetch(LOGIN, {
          method: "POST",
          body: {
            email: userEmail.value,
            password: userPass.value,
            captcha_token: captchaToken,
            device_id: deviceIdCookie.value
              ? deviceIdCookie.value
              : deviceId.value,
          },
        });

        if (token) {
          await setAuthCookies(token);
          if (tokenCookie.value) {
            if (user && user.id) {
              setUser(user);
              setTimeout(() => {
                joinToChannel(user.id);
                setAllNotifications();
              }, 1000);
            }

            nuxtApp.$toast("success", {
              message: t("messages.login_success"),
              className: "toasted-bg-success",
            });
            emit("loggedInSuccess");
            // setRememberMeCookie(userEmail.value, userPass.value);
            setTimeout(() => {
              if (
                redirectUrl.value &&
                redirectUrl.value !== null &&
                redirectUrl.value !== undefined
              ) {
                router.push(localePath(`${redirectUrl.value}`));
              } else {
                router.push(localePath("/"));
              }
            }, 1000);
          } else {
            nuxtApp.$toast("error", {
              message: message,
              className: "toasted-bg-alert",
            });
          }
        } else {
          nuxtApp.$toast("error", {
            message: message,
            className: "toasted-bg-alert",
          });
        }
      } catch (error: any) {
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message.password
            ? error?.response?._data?.message.password
            : error?.response?._data?.message.email
            ? error?.response?._data?.message.email
            : error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
        if (
          error?.response?.status === 401 ||
          error?.response?.status === 404
        ) {
          userEmail.value = "";
          userPass.value = "";
          setTimeout(() => {
            router.push(localePath("/auth/security"));
          }, 200);
        }
      } finally {
        setTimeout(() => {
          loading.value = false;
        }, 1000);
      }
    }
  }
};
const setRememberMeCookie = (email: string, password: string) => {
  rememberMeCookie.value = { email, password };
};

const reloadCaptcha = () => {
  if (window.grecaptcha && window.grecaptcha.enterprise) {
    recaptchaRef.value = window.grecaptcha.enterprise;
  }
};
onMounted(() => {
  if (tokenCookie.value) {
    router.push(localePath("/"));
  }
  generateDeviceFingerprint();

  // Watch for the script to be loaded
  watchEffect(() => {
    reloadCaptcha();
  });
  window.scrollTo(0, 0);
});

const showEmailComp = ref(true);
const showPasswordComp = ref(false);
const showOtpComp = ref(false);
const showRegistrationComp = ref(false);
const otpCode = ref("");
const emailChecking = ref(false);

const submitEmail = async () => {
  emailChecking.value = true;
  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "USER_EXISTS",
      }
    );
    if (captchaToken) {
      try {
        const data: any = await $fetch(CHECH_USER_EXISTS, {
          method: "POST",
          body: {
            email: userEmail.value,
            captcha_token: captchaToken,
          },
        });
        if (data.exist && data.has_password) {
          if (!deviceIdCookie.value) {
            deviceIdCookie.value = deviceId.value;
          }
          showEmailComp.value = false;
          showPasswordComp.value = true;
          emailChecking.value = false;
        } else if (!data.exist && !data.has_password) {
          showEmailComp.value = false;
          showOtpComp.value = true;
          emailChecking.value = false;
          nuxtApp.$toast("clear");
          nuxtApp.$toast("success", {
            message: data.message,
            className: "toasted-bg-success",
          });
        }
      } catch (error) {
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
      } finally {
        emailChecking.value = false;
      }
    }
  }
};

const rememberToken = ref(null);

const setCode = (code: any) => {
  otpCode.value = code;
};

const submitOtp = async () => {
  loading.value = true;
  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "VERIFY_EMAIL",
      }
    );
    if (captchaToken) {
      try {
        const data: any = await $fetch(VERIFY_CODE, {
          method: "POST",
          body: {
            email: userEmail.value,
            code: otpCode.value,
            captcha_token: captchaToken,
          },
        });
        if (data.remember_token && data.message) {
          rememberToken.value = data.remember_token;
          showOtpComp.value = false;
          showRegistrationComp.value = true;
          nuxtApp.$toast("success", {
            message: data.message,
            className: "toasted-bg-success",
          });
        } else if (data.message) {
          nuxtApp.$toast("error", {
            message: data.message,
            className: "toasted-bg-alert",
          });
        }
      } catch (error) {
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
      } finally {
        loading.value = false;
      }
    }
  }
};

const registerForm = ref(null);
const handleRegister = async () => {
  loading.value = true;
  const captchaToken = await recaptchaRef.value.execute(
    config.public.recaptchaKey,
    {
      action: "SIGNUP",
    }
  );
  if (recaptchaRef.value) {
    if (captchaToken) {
      if (!termsAccepted.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: t("messages.accept_terms_privacy"),
          className: "toasted-bg-alert",
        });
        loading.value = false;
        return;
      }
      if (userPass.value === userConfPass.value) {
        if (deviceId.value !== "") {
          try {
            const userTimeZone =
              Intl.DateTimeFormat().resolvedOptions().timeZone;
            const data = await $fetch<any>(REGISTER_USER, {
              method: "POST",
              body: {
                name: fullName.value,
                email: userEmail.value,
                phone: userPhone.value,
                password: userPass.value,
                device_id: deviceId.value,
                captcha_token: captchaToken,
                timezone: userTimeZone,
                remember_token: rememberToken.value,
                is_agree_with_our_policy: 1,
              },
            });
            if (data && data?.token) {
              await setAuthCookies(data.token);
              deviceIdCookie.value = deviceId.value;
              if (tokenCookie.value) {
                if (data.user && data.user.id) {
                  setUser(data.user);
                  setTimeout(() => {
                    joinToChannel(data.user.id);
                    setAllNotifications();
                  }, 1000);
                }

                nuxtApp.$toast("success", {
                  message: t("messages.login_success"),
                  className: "toasted-bg-success",
                });
                emit("signupSuccess");
                setTimeout(() => {
                  if (
                    redirectUrl.value &&
                    redirectUrl.value !== null &&
                    redirectUrl.value !== undefined
                  ) {
                    router.push(localePath(`${redirectUrl.value}`));
                  } else {
                    router.push(localePath("/"));
                  }
                }, 1000);
              } else {
                nuxtApp.$toast("error", {
                  message: data?.message,
                  className: "toasted-bg-alert",
                });
              }
            } else {
              nuxtApp.$toast("error", {
                message: t("messages.something_wrong"),
                className: "toasted-bg-alert",
              });
            }
          } catch (error: any) {
            loading.value = false;
            if (error.response && error.response.status === 422) {
              const errorMessage = error.response._data.message;
              const { phone, ...rest } = error.response._data.errors;
              errorMessageInPhone.value = phone;
              registerForm.value?.setErrors(rest);
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: errorMessage,
                className: "toasted-bg-alert",
              });
            } else {
              const errorMessage = error.response._data.message;
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: errorMessage,
                className: "toasted-bg-alert",
              });
            }
          } finally {
            setTimeout(() => {
              loading.value = false;
            }, 1000);
          }
        } else {
          loading.value = false;
        }
      } else {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: t("messages.password_not_match"),
          className: "toasted-bg-alert",
        });
        loading.value = false;
      }
    } else {
      loading.value = false;
    }
  }
};

// registaration
const fullName = ref("");
const userConfPass = ref("");
const userPhone = ref("");
const showPass = ref(false);
const showConfirmPassword = ref(false);
const termsAccepted = ref(false);
const isPasswordMatch = computed(() => {
  return userPass.value === userConfPass.value;
});

const previous = () => {
  if (showPasswordComp.value) {
    showPasswordComp.value = false;
    showEmailComp.value = true;
  } else if (showOtpComp.value) {
    showOtpComp.value = false;
    showEmailComp.value = true;
  } else if (showRegistrationComp.value) {
    showRegistrationComp.value = false;
    showOtpComp.value = true;
  }
};

const errorInPhone = ref(true);
const errorMessageInPhone = ref(<any>[]);

const resetError = () => {
  errorInPhone.value = false;
  errorMessageInPhone.value = [];
};
const getPhoneData = (data: any) => {
  userPhone.value = data.phone;
  errorInPhone.value = data.error;
};
</script>
<template>
  <div>
    <div
      class="box-shadow w-full sm:w-[520px] px-2.5 xs:px-4 py-6 md:px-8 font-semibold text-lg"
    >
      <div v-if="showEmailComp">
        <h1 class="text-xl font-semibold mb-6">
          {{ $t("proceed_with_email") }}
        </h1>
        <Form @submit="submitEmail" v-slot="{ meta }" class="">
          <Field
            v-model="userEmail"
            name="email"
            type="email"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
            :placeholder="$t('email_address')"
            :rules="validateEmail"
          />
          <ErrorMessage class="error-message" name="email" />
          <button
            class="mt-12 px-4 w-full h-[50px] text-lg bg-black font-semibold rounded text-white disabled:opacity-70"
            type="submit"
            :disabled="!meta.valid || emailChecking"
          >
            {{ $t("submit") }}
          </button>
        </Form>
      </div>
      <div v-if="showPasswordComp">
        <h1 class="text-xl font-semibold mb-6">
          {{ $t("proceed_with_pass") }}
        </h1>
        <Form @submit="handleLogin" v-slot="{ meta }" class="">
          <div class="relative">
            <Field
              v-model="userPass"
              name="password"
              :type="showPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('password')"
              :rules="validatePassword"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPassword"
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message" name="password" />
          <div class="flex flex-row items-center justify-between pt-2.5">
            <div></div>
            <NuxtLink
              :to="localePath('/auth/forgot-password')"
              class="opacity-100 text-primary-red"
              >{{ $t("forgot_password") }}</NuxtLink
            >
          </div>
          <button
            class="mt-8 px-4 w-full h-[50px] text-lg bg-black font-semibold rounded text-white disabled:opacity-70"
            type="submit"
            :disabled="!meta.valid || loading"
          >
            {{ $t("submit") }}
          </button>
        </Form>
      </div>
      <div v-if="showOtpComp">
        <h1 class="text-xl font-semibold mb-6">{{ $t("confirm_email") }}</h1>
        <p class="mb-6 text-base font-normal">
          {{ $t("this_your") }} {{ userEmail }} {{ $t("otp_text") }}
        </p>
        <Form @submit="submitOtp" v-slot="{ meta }" class="">
          <BaseOtpVerification
            class="mt-8"
            input-border-style="border-b-2 border-black"
            input-text-style="text-2xl text-center text-black"
            @verified="setCode"
          />
          <button
            class="mt-12 px-4 w-full h-[50px] text-lg bg-black font-semibold rounded text-white disabled:opacity-70"
            type="submit"
            :disabled="!meta.valid || otpCode.length < 6 || loading"
          >
            {{ $t("submit") }}
          </button>
          <div
            class="flex justify-center items-center gap-2 mt-3.5 text-base font-normal"
          >
            <p>{{ $t("didnt_get_code") }}</p>
            <p
              @click="!emailChecking ? submitEmail() : ''"
              :class="
                !emailChecking ? 'cursor-pointer hover:font-semibold' : ''
              "
            >
              {{ $t("send_again") }}
            </p>
          </div>
        </Form>
      </div>
      <div v-if="showRegistrationComp">
        <h1 class="text-xl font-semibold mb-6">{{ $t("give_yr_info") }}</h1>
        <Form
          v-if="true"
          ref="registerForm"
          @submit="handleRegister"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <Field
            v-model="fullName"
            name="fullName"
            type="text"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
            :placeholder="$t('full_name')"
            :rules="isRequired"
          />
          <ErrorMessage class="error-message block" name="firstName" />

          <BasePhoneNumberInput
            @phone="getPhoneData"
            @resetError="resetError"
            :error="
              errorMessageInPhone && errorMessageInPhone.length > 0
                ? errorMessageInPhone[0]
                : ''
            "
          />

          <div class="relative">
            <Field
              v-model="userPass"
              name="password"
              :type="showPass ? 'text' : 'password'"
              class="w-full px-6 p-2 pr-10 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('password')"
              :rules="validatePassword"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPass"
                @click="showPass = !showPass"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showPass = !showPass"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message" name="password" />
          <div class="relative">
            <Field
              v-model="userConfPass"
              name="confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 pr-10 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('confirm_password')"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showConfirmPassword"
                @click="showConfirmPassword = !showConfirmPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showConfirmPassword = !showConfirmPassword"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <span
            v-if="userPass != userConfPass && userConfPass"
            class="text-base font-normal text-[#ec1f27] pl-1"
            >{{ $t("messages.password_not_match") }}</span
          >

          <div class="flex items-start gap-3">
            <input
              class="h-5 w-5 min-w-[20px]"
              type="checkbox"
              name="termsAccepted"
              id="termsAccepted"
              v-model="termsAccepted"
            />
            <p class="text-sm text-black font-semibold">
              {{ $t("account_terms") }}
              <NuxtLink
                :to="localePath('/terms-of-use')"
                class="text-primary-red"
                >{{ $t("terms_of_use") }}</NuxtLink
              >
              {{ $t("and_2") }}
              <NuxtLink
                :to="localePath('/privacy-policy')"
                class="text-primary-red"
                >{{ $t("privacy_policy") }}</NuxtLink
              >
              {{ $t("give_permission") }}
            </p>
          </div>
          <button
            class="!mt-8 px-4 w-full h-[50px] text-lg bg-black font-semibold rounded text-white disabled:opacity-70"
            type="submit"
            :disabled="
              !meta.valid ||
              loading ||
              !termsAccepted ||
              !isPasswordMatch ||
              errorInPhone ||
              !userPhone
            "
          >
            {{ $t("submit") }}
          </button>
        </Form>
      </div>
    </div>
    <div
      class="w-full sm:w-[520px] px-2.5 xs:px-4 py-2 md:px-8 flex items-center justify-center"
    >
      <button
        v-if="!showEmailComp"
        @click="previous"
        class="border border-black px-4 py-1 rounded-sm hover:bg-primary-red hover:text-white font-semibold hover:border-primary-red flex items-center gap-2"
      >
        <IconsLeftArrow class="w-4" /> <span>{{ $t("previous") }}</span>
      </button>
    </div>
  </div>
</template>
<style scoped>
.verifyInput {
  display: block;
  outline: none;
  border: none;
  height: 2em;
  font-size: 16px;
  margin-bottom: 1px;
  border-bottom: 1px solid #333;
}

.verifyInput:focus {
  border-bottom: 1px solid #0572ce;
  box-shadow: 0 1px 0 0 #0572ce;
}

.box-shadow {
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.25),
    0px 1px 2px 0px rgba(0, 0, 0, 0.25);
}
</style>

<script setup>
const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
  cardIndex: {
    type: Number,
    required: true,
  },
});

const className = computed(() => {
  const colorClass = {
    textColor: "",
    bgColor: "",
  };
  const remainder = props.cardIndex % 3;
  if (remainder === 0) {
    colorClass.textColor = "text-[#67912C]";
    colorClass.bgColor =
      "bg-[linear-gradient(223deg,_#f7fce8_6.44%,_#fcfef7_93.49%)]";
  } else if (remainder === 1) {
    colorClass.textColor = "text-[#FF9900]";
    colorClass.bgColor =
      "bg-[linear-gradient(223deg,_#FFF7EB_6.44%,_#FFFCF8_93.49%)]";
  } else {
    colorClass.textColor = "text-[#10A7CA]";
    colorClass.bgColor =
      "bg-[linear-gradient(223deg,_#EBF9FD_6.44%,_#F7FDFD_93.49%)]";
  }
  return colorClass;
});
</script>

<template>
  <div class="card" :class="[className.bgColor]">
    <div class="w-full h-full text-left space-y-5">
      <p
        v-if="card.is_show_percentage"
        class="text-2xl md:text-[28px] leading-normal font-semibold"
        :class="[className.textColor]"
      >
        {{ card.percentage }}%
      </p>
      <h3
        v-if="card.title"
        class="text-2xl md:text-[28px] leading-normal font-semibold"
        :class="[className.textColor]"
      >
        {{ card.title }}
      </h3>
      <div
        v-if="card.content"
        class="text-[#605F62] text-base break-word"
        v-html="card.content"
      ></div>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply w-full h-full min-h-[440px] rounded-2xl p-6 md:p-8;
  box-shadow: 0px 10px 20px 0px rgba(183, 188, 201, 0.16);
}
</style>

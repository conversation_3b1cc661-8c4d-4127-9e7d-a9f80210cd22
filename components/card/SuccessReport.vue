<script setup>
import { useIndexStore } from "~/stores/index";

const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
  cardClass: {
    type: String,
    default: "flex-col lg:flex-row",
  },
  bgDotsClass: {
    type: String,
    default: "top-0 left-0",
  },
  mediaClass: {
    type: String,
    default: "w-full aspect-video rounded-[20px]",
  },
});

const { card } = toRefs(props);

const { setMedia } = useIndexStore();

const setModalData = (data) => {
  setMedia(data);
};
</script>

<template>
  <div class="card relative bg-[#FFF4EC]">
    <IconsBgDots class="absolute" :class="bgDotsClass" />
    <div class="min-h-[327px] w-full">
      <ClientOnly>
        <BaseSlider
          :sliders="card"
          :isSingleSlider="true"
          :showPagination="false"
          carouselItemClass="p-4"
          navigationClass="success-report"
          class="w-full rounded-[20px]"
        >
          <template #slider="{ data }">
            <div
              class="w-full h-full flex items-center justify-start lg:justify-between gap-10 xl:gap-12"
              :class="[cardClass]"
            >
              <img
                v-if="data.type === 'image'"
                class="object-fill object-top lg:w-1/2 z-[1]"
                :class="mediaClass"
                :src="data.media_link"
                :alt="data.title"
              />
              <BaseThumbWithPlayBtn
                v-else-if="data.type === 'video'"
                :coverImage="data.cover_image"
                :altText="data.title"
                :thumbClass="mediaClass"
                class="lg:w-1/2"
                @toggleMedia="setModalData(data)"
              />
              <div class="w-full max-w-[457px] lg:w-1/2 text-center lg:text-left">
                <h2
                  class="heading-primary text-black text-3xl mb-3 line-clamp-2"
                >
                  {{ data.title }}
                </h2>
                <p class="text-primary line-clamp-4">
                  {{ data.sub_title }}
                </p>
                <NuxtLink
                  v-if="data.url"
                  :to="data.url"
                  class="red-button bg-transparent text-primary-red text-sm md:text-base lg:text-lg border border-black py-1.5 w-44 mx-auto lg:ml-0 mt-14"
                >
                  <span>{{ $t("view_course_btn") }}</span>
                </NuxtLink>
              </div>
            </div>
          </template>
        </BaseSlider>
      </ClientOnly>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply w-full h-full rounded-[40px] p-4 md:p-10 xl:pl-[96px] xl:pr-[135px];
  box-shadow: 0px 10px 20px 0px rgba(183, 188, 201, 0.16);
}
</style>

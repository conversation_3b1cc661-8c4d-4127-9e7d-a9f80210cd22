<script setup>
import courseThumbnail from "~/assets/img/default/course-thumbnail.png";
defineProps({
  card: {
    type: Object,
    required: true,
  },
});

const localePath = useLocalePath();
</script>

<template>
  <div class="card w-full h-full flex flex-col">
    <img
      :src="card?.banner_url ? card.banner_url : courseThumbnail"
      class="object-cover w-full aspect-[378/212] rounded-t-2xl"
      :alt="card?.title"
    />

    <div class="p-5 flex flex-col flex-grow">
      <div class="pt-3 text-start flex-grow">
        <h2 class="text-2xl font-semibold line-clamp-1 mb-3">
          {{ card?.title }}
        </h2>
        <p class="text-base line-clamp-2 mb-5">
          {{ card?.subtitle }}
        </p>
      </div>
      <NuxtLink
        :to="localePath(`/precheckout/${card?.slug}`)"
        class="view-more-btn"
      >
        {{ $t("view_course") }}
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply bg-white rounded-2xl text-black;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
}
.card:hover {
  box-shadow: 0px 20px 40px 0px rgba(183, 188, 201, 0.25);
}
</style>

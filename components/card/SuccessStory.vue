<script setup>
import { useIndexStore } from "~/stores/index";

const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
  mediaClass: {
    type: String,
    default: "w-full aspect-video rounded-t-2xl",
  },
});

const localePath = useLocalePath();
const { card } = toRefs(props);
const { setMedia } = useIndexStore();

const setModalData = () => {
  setMedia(card.value);
};
</script>

<template>
  <div class="card w-full h-full flex flex-col">
    <div
      v-if="card?.media_url"
      class="w-full rounded-t-2xl overflow-hidden"
      :class="mediaClass"
    >
      <BaseThumbWithPlayBtn
        v-if="card.type === 'video'"
        :coverImage="card.cover_image"
        :altText="card.title"
        :thumbClass="mediaClass"
        @toggleMedia="setModalData"
      />
      <img
        v-else
        :src="card.media_url"
        class="object-cover"
        :class="mediaClass"
        :alt="card.title"
      />
    </div>
    <div
      class="text-lg px-5 py-[13px] bg-[#EDEDED] text-[#A2D55A] font-medium flex items-center justify-between"
    >
      <div class="flex items-center justify-start space-x-2.5">
        <IconsCalender class="text-black" />
        <span>{{ $dayjs(card.created_at).format("DD/MM/YYYY") }}</span>
      </div>
      <p class="flex gap-1">
        <span class="text-black line-clamp-1">By</span>
        <span>{{ card?.success_story_students_name }}</span>
      </p>
    </div>
    <div class="p-5 flex flex-col flex-grow">
      <div class="text-start flex-grow">
        <h2 class="text-xl font-semibold line-clamp-2 mb-3">
          {{ card.title }}
        </h2>
        <p
          v-if="card?.description"
          class="text-base line-clamp-2 mb-5 flex flex-col flex-grow"
        >
          {{ card?.description }}
        </p>
      </div>
      <NuxtLink
        :to="localePath(`/success-story`)"
        class="view-more-btn bg-[#A2D55A]/10 text-[#A2D55A]"
      >
        {{ $t("see_more") }}
      </NuxtLink>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply bg-white rounded-2xl text-black;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease-in-out;
}
.card:hover {
  box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.1);
}
</style>

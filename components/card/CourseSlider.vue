<script setup>
import courseThumbnail from "~/assets/img/default/course-thumbnail.png";

defineProps({
  card: {
    type: Object,
    required: true,
  },
  mediaClass: {
    type: String,
    default: "w-full aspect-[338/190] rounded-[10px]",
  },
});

const localePath = useLocalePath();
</script>

<template>
  <div class="card w-full">
    <img
      :class="mediaClass"
      :src="card.banner_url ? card.banner_url : courseThumbnail"
      :alt="card.title"
    />
    <h2
      class="text-xl font-semibold text-black leading-8 text-start line-clamp-1"
    >
      {{ card.title }}
    </h2>
    <div class="flex items-center justify-between text-sm text-black pb-2.5">
      <div class="flex items-center space-x-1">
        <IconsVideo class="rounded-full" />
        <p>{{ card.total_videos }}</p>
      </div>
      <div class="flex items-center space-x-1">
        <IconsVideoLength class="rounded-full" />
        <p>{{ card.total_duration }}</p>
      </div>
      <div class="flex items-center space-x-1">
        <IconsPerson class="rounded-full" />
        <p>{{ card.total_purchase }}</p>
      </div>
    </div>
    <NuxtLink
      :to="localePath(`/precheckout/${card.slug}`)"
      class="view-more-btn"
      >{{ $t("view_more") }}</NuxtLink
    >
  </div>
</template>

<style scoped>
.card {
  @apply bg-white rounded-2xl p-5 space-y-5;
  border: 0.5px solid #ec1f274c;
  transition: all 0.3s ease-in-out;
}
.card:hover {
  box-shadow: 0px 20px 40px 0px rgba(183, 188, 201, 0.25);
}
</style>

<script setup>
defineProps({
  slider: {
    type: Object,
    required: true,
  },
  section: {
    type: Object,
    required: true,
  },
  cardClass: {
    type: String,
    default: "flex-col lg:flex-row",
  },
  bgDotsClass: {
    type: String,
    default: "top-0 left-0",
  },
});
</script>

<template>
  <div
    class="card flex items-center justify-between relative gap-10 xl:gap-30"
    :class="[cardClass, section.bgColor]"
  >
    <IconsBgDots class="absolute" :class="bgDotsClass" />
    <div class="lg:max-w-[530px] 2xl:max-w-[600px] w-full lg:w-1/2">
      <ClientOnly>
        <BaseSlider
          :sliders="slider"
          :isSingleSlider="true"
          :showPagination="false"
          carouselItemClass=""
          :navigationClass="section.section"
        >
          <template #slider="{ data }">
            <CardImageVideo
              :card="data"
              :showDescription="false"
              :openInModal="true"
            />
          </template>
        </BaseSlider>
      </ClientOnly>
    </div>
    <div
      class="w-full lg:w-1/2 lg:max-w-[530px] 2xl:max-w-[600px] text-center lg:text-left"
    >
      <h2 class="heading-primary mb-3">
        {{ section.title }}
      </h2>
      <p class="text-primary">
        {{ section.description }}
      </p>
    </div>
  </div>
</template>

<style scoped>
.card {
  @apply w-full h-full rounded-[40px] px-10 py-12;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.25);
}
</style>

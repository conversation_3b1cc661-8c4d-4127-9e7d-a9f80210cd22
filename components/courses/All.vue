<script setup>
defineProps({
  courses: {
    type: Array,
    required: true,
  },
});
</script>
<template>
  <section class="flex items-center justify-center">
    <div
      v-if="courses && courses.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[33px]"
    >
      <CardCourse
        v-for="course in courses"
        :key="course.id"
        :card="course"
      />
    </div>
    <NoPageFound v-else minHeight="220" />
  </section>
</template>

<script setup lang="ts">
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";
import { useAuthStore } from "~/stores/auth";
import { useDashboardStore } from "~/stores/dashboard";

const { apiBaseUrl, siteUlr } = useUrls();
const route = useRoute();
const router = useRouter();
const selectedRadioIndex = ref(0);
const showSubscriptionModal = ref(false);
const selectedProduct = ref("");
const selectedItemId = ref(0);
const selectedSubscription = ref("");
const tokenCookie = useCookie("token");
const oldCardData: any = ref([]);
const allSubscriptions: any = ref([]);
const nuxtApp = useNuxtApp();
const { isBetween, isLargerOrEqual } = useBreakpoints();
const localePath = useLocalePath();
const { t } = useI18n();
const isProcessing = ref(false);

const localTokenFromIOS = computed(() =>
  route.query?.iosToken ? (route.query.iosToken as string) : null
);

const textCount = computed(() => {
  if (isLargerOrEqual(1920)) {
    return 90;
  } else if (isBetween(1700, 1919)) {
    return 80;
  } else if (isBetween(1440, 1700)) {
    return 60;
  } else if (isBetween(1280, 1440)) {
    return 50;
  } else if (isBetween(1100, 1280)) {
    return 40;
  } else if (isBetween(1024, 1100)) {
    return 24;
  } else if (isBetween(900, 1024)) {
    return 170;
  } else if (isBetween(768, 900)) {
    return 140;
  } else if (isBetween(640, 768)) {
    return 100;
  } else if (isBetween(500, 640)) {
    return 70;
  } else if (isBetween(400, 500)) {
    return 50;
  } else {
    return 40;
  }
});

const courseDetails = ref<any>({});
const cartDataCookie: any = useCookie("cartData");

const defaultSubscriptionId = computed(
  () => courseDetails.value?.default_subscription_id
);

const addToCart = async (product: any, itemId: number) => {
  if (product.purchase_allowed) {
    // showSubscriptionModal.value = !showSubscriptionModal.value;

    selectedProduct.value = product;
    selectedItemId.value = itemId;
    const cartDataCookie: any = useCookie("cartData");
    selectedSubscription.value = product.default_subscription_id;

    if (selectedSubscription.value !== "") {
      if (tokenCookie.value) {
        try {
          const checkItemExist = ref(false);
          const { data }: any = await useFetch(`${apiBaseUrl}/carts`, {
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
          });

          data.value.data.forEach((singleItem: any) => {
            if (singleItem.item.id === itemId) {
              checkItemExist.value = true;
              nuxtApp.$toast("error", {
                message: t("messages.already_added_to_cart"),
                className: "toasted-bg-alert",
              });
              if (localTokenFromIOS.value) {
                router.replace(localePath("/cart?iosPayment=true"));
              } else {
                router.push(localePath("/cart"));
              }
            }
          });

          if (!checkItemExist.value) {
            const { data, error }: any = await useFetch(
              `${apiBaseUrl}/carts/add`,
              {
                method: "POST",
                headers: {
                  Authorization: `Bearer ${tokenCookie.value}`,
                },
                body: {
                  item_id: itemId,
                  subscription_id: selectedSubscription.value,
                },
              }
            );
            if (!error.value && data.value) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("success", {
                message: data.value.message,
                className: "toasted-bg-success",
              });
              setTimeout(() => {
                if (localTokenFromIOS.value) {
                  router.replace(localePath("/cart?iosPayment=true"));
                } else {
                  router.push(localePath("/cart"));
                }
              }, 500);
            }

            return data.value.cart;
          }
        } catch (err) {
          console.log(err);
        }
      } else {
        const addNewProduct = {
          id: product.id,
          item_category_id: product.item_category_id,
          title: product.title,
          banner_url: product.banner_url,
          price: product.price,
          special_price: product.special_price,
          subscription_id: selectedSubscription.value,
          monthly_service_charge: product.monthly_service_charge,
          defaultSubcription: product.defaultSubcription,
          slug: product.slug,
        };

        if (cartDataCookie.value?.length > 0) {
          const isExist = cartDataCookie.value.find(
            (singleCartData: any) => singleCartData.id === addNewProduct.id
          );
          if (isExist) {
            nuxtApp.$toast("error", {
              message: t("messages.already_added_to_cart"),
              className: "toasted-bg-success",
            });
            router.push(localePath("/cart"));
          } else {
            cartDataCookie.value = JSON.stringify([]);
            setTimeout(() => {
              cartDataCookie.value = JSON.stringify([addNewProduct]);
              nuxtApp.$toast("success", {
                message: t("messages.added_to_cart"),
                className: "toasted-bg-success",
              });
              setTimeout(() => {
                router.push(localePath("/cart"));
              }, 300);
            }, 300);

            // cartDataCookie.value = JSON.stringify(cartDataCookie.value);
            // oldCardData.value = JSON.parse(cartDataCookie.value);
            // cartDataCookie.value = JSON.stringify([]);
            // oldCardData.value.push(addNewProduct);
            // cartDataCookie.value = JSON.stringify(oldCardData.value);
            // nuxtApp.$toast("success", {
            //   message: t("messages.added_to_cart"),
            //   className: "toasted-bg-success",
            // });
            // router.push(localePath("/cart"));
          }
        } else {
          // oldCardData.value.push(addNewProduct);
          if (cartDataCookie.value && cartDataCookie.value.length === 0) {
            cartDataCookie.value = null;
            setTimeout(() => {
              cartDataCookie.value = [addNewProduct];
            }, 300);
          } else {
            cartDataCookie.value = JSON.stringify([addNewProduct]);
          }
          setTimeout(() => {
            nuxtApp.$toast("success", {
              message: t("messages.added_to_cart"),
              className: "toasted-bg-success",
            });
            router.push(localePath("/cart"));
          }, 500);
        }
      }
    }
  } else {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: t("messages.course_already_purchased"),
      className: "toasted-bg-alert",
    });
  }
};

const selectedSubscriptionId = ref(0);

// for subscription modal
const getSubscription = async () => {
  const { data, pending, error }: any = await useFetch(
    `${apiBaseUrl}/subscriptions/video/${courseDetails.value?.id}`
  );

  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        allSubscriptions.value = data.value.data;
        if (defaultSubscriptionId.value) {
          selectedSubscriptionId.value = defaultSubscriptionId.value;
        } else {
          selectedSubscriptionId.value = allSubscriptions.value[0]?.id;
          selectedSubscription.value = allSubscriptions.value[0]?.id;
        }
      } else if (error.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error.value?.data.message,
          className: "toasted-bg-alert",
        });
      }
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};
// getSubscription();

const handleSubscription = (id: any) => {
  selectedSubscription.value = id;
  addToCart(selectedProduct.value, selectedItemId.value);
};

//youMightBeThinking
const expandedYouMightBeThinking = ref<number[]>([]);
const toggleYouMightBeThinking = (id: number) => {
  if (expandedYouMightBeThinking.value.includes(id)) {
    expandedYouMightBeThinking.value = expandedYouMightBeThinking.value.filter(
      (item) => item !== id
    );
  } else {
    expandedYouMightBeThinking.value.push(id);
  }
};
const isExpandedYouMightBeThinking = (id: number) => {
  return expandedYouMightBeThinking.value.includes(id);
};
// Course Details
const expandedCourseDetails = ref(<number[]>[]);
const toggleCourseDetail = (id: number) => {
  if (expandedCourseDetails.value.includes(id)) {
    expandedCourseDetails.value = expandedCourseDetails.value.filter(
      (item) => item !== id
    );
  } else {
    expandedCourseDetails.value.push(id);
  }
};
const isExpandedCourseDetail = (id: number) => {
  return expandedCourseDetails.value.includes(id);
};
// FAQ
const expandedItems = ref(<number[]>[]);
const toggle = (id: number) => {
  if (expandedItems.value.includes(id)) {
    expandedItems.value = expandedItems.value.filter((item) => item !== id);
  } else {
    expandedItems.value.push(id);
  }
};
const isExpanded = (id: number) => {
  return expandedItems.value.includes(id);
};
const setSelectedRadioIndex = (index: number, id: number) => {
  selectedRadioIndex.value = index;
  selectedSubscriptionId.value = id;
};

// course content
const selectedLessonDetails = ref<any>({});
const selectedVideoDetails = ref({});

const currentSelectedLessonId = ref(-1);
const currentSelectedVideoId = ref(-1);
const selectVideo = (selectedId: number) => {
  selectedVideoDetails.value = selectedLessonDetails.value.videos.find(
    (singleVideo: any) => singleVideo.id === selectedId
  );

  currentSelectedVideoId.value = selectedId;

  // setTimeout(() => {
  //   const body = document.getElementsByTagName("iframe")[0];
  // }, 1000);
};

const selectLesson = (selectedId: number) => {
  if (currentSelectedLessonId.value === selectedId) {
    currentSelectedLessonId.value = -1;
  } else {
    selectedLessonDetails.value = courseDetails.value.units.find(
      (singleVideo: any) => singleVideo.id === selectedId
    );
    currentSelectedLessonId.value = selectedId;
    selectVideo(courseDetails.value?.units[0]?.videos[0]?.id);
  }
};

const initialExpand = () => {
  if (courseDetails.value) {
    if (courseDetails.value?.youMightBeThinking?.length > 0) {
      toggleYouMightBeThinking(courseDetails.value?.youMightBeThinking[0]?.id);
    }
    if (courseDetails.value?.allDescriptions?.length > 0) {
      toggleCourseDetail(courseDetails.value?.allDescriptions[0]?.id);
    }
    if (courseDetails.value?.faqs?.length > 0) {
      toggle(courseDetails.value?.faqs[0]?.id);
    }
    if (courseDetails.value?.units?.length > 0) {
      selectLesson(courseDetails.value?.units[0]?.id);
    }
  }
};

const getCourseDetails = async () => {
  isProcessing.value = true;
  const { data, pending, error }: any = await useFetch(
    `${apiBaseUrl}/courses/${route.params.slug}`,
    {
      headers: {
        Authorization: `Bearer ${tokenCookie.value ? tokenCookie.value : ""}`,
      },
    }
  );

  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        courseDetails.value = data.value.data;
        initialExpand();
        getSubscription();
      } else if (error.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error.value?.data.message,
          className: "toasted-bg-success",
        });
      }
      isProcessing.value = false;
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};
await getCourseDetails();

useSeoMeta({
  title: () => `${courseDetails?.value?.title} | Sk Mobile School`,
  ogTitle: () => `${courseDetails?.value?.title} | Sk Mobile School`,
  description: () => courseDetails?.value?.subtitle,
  ogDescription: () => courseDetails?.value?.subtitle,
  ogImage: () => courseDetails?.value?.banner_url,
  twitterCard: "summary_large_image",
});

const scrollTop = ref(-1);
const isScrollOnBottom = ref(false);
const handleScroll = (e: any) => {
  scrollTop.value = e.target.scrollTop;
  if (
    e.target.scrollHeight - e.target.scrollTop - 20 <=
    e.target.clientHeight
  ) {
    isScrollOnBottom.value = true;
  } else {
    isScrollOnBottom.value = false;
  }
};

watch(showSubscriptionModal, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      const el = document.getElementById("subscriptionModalScroll");
      el?.addEventListener("scroll", handleScroll);
      handleScroll({ target: el });
    }, 300);
  } else {
    const el2 = document.getElementById("subscriptionModalScroll");
    el2?.removeEventListener("scroll", handleScroll);
    scrollTop.value = -1;
  }
});
const { joinToChannel } = useNotification();
const { setAllNotifications } = useDashboardStore();
const { setUser } = useAuthStore();
const { ME } = useUrls();
const { logout } = useAuth();
const verifyIOSToken = async () => {
  if (localTokenFromIOS.value) {
    try {
      const data: any = await $fetch(`${apiBaseUrl}/receive-token`, {
        method: "POST",
        body: {
          encoded_token: localTokenFromIOS.value,
        },
      });
      if (data.decoded_token) {
        tokenCookie.value = data.decoded_token;
        try {
          const user = (await $fetch(ME, {
            headers: {
              Authorization: `Bearer ${data.decoded_token}`,
            },
          })) as any;
          if (user && user.id) {
            joinToChannel(user.id);
            setAllNotifications();
            setUser(user);
          }
        } catch (e) {
          await logout(tokenCookie.value || "");
        }
      }
    } catch (error) {
      console.log(error);
    }
  }
};

onMounted(async () => {
  await nextTick();
  await verifyIOSToken();
  window.scrollTo(0, 0);
});

const selectedInstructors = ref<number[]>([]);
const toggleInstructionDetail = (id: number) => {
  if (selectedInstructors.value.includes(id)) {
    selectedInstructors.value = selectedInstructors.value.filter(
      (item) => item !== id
    );
  } else {
    selectedInstructors.value.push(id);
  }
};
const isShowMore = (id: number) => {
  return selectedInstructors.value.includes(id);
};

const showShareModal = ref(false);
const courseUrl = computed(() => {
  return `${siteUlr}/precheckout/${route.params.slug}`;
});
const overViewMedia = ref(false);
</script>
<template>
  <div
    class="custom-container pt-12 pb-20 flex gap-10 flex-col-reverse lg:flex-row"
    :class="isProcessing ? 'justify-center' : 'justify-between'"
  >
    <div v-if="isProcessing">
      <IconsIsLoading class="h-24" />
      <p class="text-lg tex-center flex justify-center">Please wait...</p>
    </div>
    <div
      v-if="courseDetails?.title"
      class="w-full lg:w-[calc(100%-420px)] xl:w-[calc(100%-500px)] 2xl:w-[calc(100%-600px)] flex flex-col gap-14 lg:gap-20 pt-5 lg:pt-0 pr-3"
    >
      <div class="block text-center md:text-left">
        <h1
          class="text-3xl md:text-[40px] font-bold text-primary-red md:leading-[3rem]"
        >
          {{ courseDetails?.title }}
        </h1>
        <div
          v-if="courseDetails.short_description !== null"
          class="mt-7 text-base md:text-lg text-justify"
          v-html="courseDetails?.short_description"
        ></div>
      </div>
      <div v-if="courseDetails.instructors.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("course_instructors") }}
        </h4>
        <div
          class="grid grid-cols-1 lg:grid-cols-2 mt-5 gap-4 border-2 border-gray-200 px-5 pt-7 pb-14 rounded-lg"
        >
          <div
            v-for="item in courseDetails.instructors"
            :key="item.id"
            class="flex gap-2.5"
          >
            <div class="w-20 h-20 aspect-square">
              <img
                v-if="item.image"
                :src="item.image"
                alt="Sk Mobile School instructor"
                class="w-full aspect-square object-cover border border-black rounded-md"
              />
              <div v-else>
                <ClientOnly>
                  <fa
                    class="w-full aspect-square object-cover border border-black rounded-md"
                    :icon="['fa-solid', 'user']"
                /></ClientOnly>
              </div>
            </div>
            <div class="flex flex-col text-lg">
              <p class="flex font-medium">
                <span class="line-clamp-2">{{ item.title }}</span>
                <IconsChevronDown class="-rotate-90 w-4 hidden" />
              </p>
              <div v-if="item.description">
                <span class="break-word">{{
                  isShowMore(item.id)
                    ? item.description
                    : item.description.slice(0, textCount)
                }}</span>
                <span
                  v-if="
                    item.description.length > textCount && !isShowMore(item.id)
                  "
                  >...</span
                >
              </div>
              <button
                v-if="item.description && item.description.length > textCount"
                @click="toggleInstructionDetail(item.id)"
                class="font-medium hover:font-bold w-24 text-left"
              >
                {{ !isShowMore(item.id) ? " See more" : " See less" }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div v-if="courseDetails.whatYouLearns.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("what_youll_learn") }}
        </h4>
        <div
          v-if="courseDetails?.whatYouLearns"
          class="grid grid-cols-1 lg:grid-cols-2 gap-5 mt-4 border-2 border-gray-200 p-5 rounded-lg"
        >
          <div
            v-for="(item, index) in courseDetails.whatYouLearns"
            class="flex items-start gap-2.5 text-lg"
          >
            <IconsCheck class="mt-1.5 min-w-[20px]" />
            <p>{{ item.title }}</p>
          </div>
        </div>
      </div>

      <div v-if="courseDetails.youMightBeThinking.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("you_may_be_thinking_to_work_online") }}
        </h4>
        <div
          v-if="courseDetails?.youMightBeThinking"
          class="flex flex-col mt-4 border-2 border-gray-200 px-4 py-2 rounded-lg"
        >
          <div
            v-for="(item, index) in courseDetails.youMightBeThinking"
            :key="item.id"
            class="flex flex-col text-lg"
            :class="
              index === courseDetails.youMightBeThinking.length - 1
                ? 'border-0'
                : 'border-b border-gray-200'
            "
          >
            <h4
              @click="toggleYouMightBeThinking(item.id)"
              class="flex items-center justify-between py-2 font-semibold cursor-pointer"
            >
              <span>{{ item.title }}</span>
              <IconsCaretDown
                class="w-3 transition-all duration-300 ease-in-out"
                :class="
                  isExpandedYouMightBeThinking(item.id)
                    ? 'rotate-180'
                    : 'rotate-0'
                "
              />
            </h4>
            <div
              v-if="isExpandedYouMightBeThinking(item.id)"
              class="py-2 opacity-70"
              v-html="item.description"
            ></div>
          </div>
        </div>
      </div>

      <div v-if="courseDetails.allDescriptions.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("course_details") }}
        </h4>
        <div
          v-if="courseDetails?.allDescriptions"
          class="flex flex-col mt-4 border-2 border-gray-200 px-4 py-2 rounded-lg"
        >
          <div
            v-for="(item, index) in courseDetails.allDescriptions"
            :key="item.id"
            class="flex flex-col text-lg"
            :class="
              index === courseDetails.allDescriptions.length - 1
                ? 'border-0'
                : 'border-b border-gray-200'
            "
          >
            <h4
              @click="toggleCourseDetail(item.id)"
              class="flex items-center justify-between py-2 font-semibold cursor-pointer"
            >
              <span>{{ item.title }}</span>
              <IconsCaretDown
                class="w-3 transition-all duration-300 ease-in-out"
                :class="
                  isExpandedCourseDetail(item.id) ? 'rotate-180' : 'rotate-0'
                "
              />
            </h4>
            <div
              v-if="isExpandedCourseDetail(item.id)"
              class="py-2 opacity-70"
              v-html="item.description"
            ></div>
          </div>
        </div>
      </div>

      <div v-if="courseDetails.units.length > 0" class="flex flex-col">
        <p class="text-[32px] font-bold text-primary-red">
          {{ $t("course_content") }}
        </p>

        <div class="mt-4 rounded-lg">
          <CoursesIndividualCourseContent
            :selected-playlist="courseDetails"
            :current-selected-lesson-id="currentSelectedLessonId"
            :is-locked="true"
            @selectLesson="selectLesson"
            @selectVideo="selectVideo"
          />
        </div>
      </div>

      <div v-if="courseDetails.reviews.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("what_students_are_saying") }}...
        </h4>
        <BaseStudentReviewCarousel
          class="w-full"
          :slider="courseDetails.reviews"
          :show-navigation="true"
        />
      </div>

      <div v-if="courseDetails.whatYouNeeds.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("what_youn_need_to_do_class") }}
        </h4>
        <div
          v-if="courseDetails?.whatYouNeeds"
          class="grid grid-cols-1 gap-5 mt-4 border-2 border-gray-200 p-5 rounded-lg"
        >
          <div
            v-for="(item, index) in courseDetails.whatYouNeeds"
            class="flex items-start gap-2.5 text-lg"
          >
            <IconsCheck class="mt-1.5 min-w-[20px]" />
            <p>{{ item.title }}</p>
          </div>
        </div>
      </div>

      <div>
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("payment_process") }}
        </h4>
        <div
          class="grid grid-cols-1 gap-5 mt-4 border-2 border-gray-200 p-5 rounded-lg"
        >
          <div class="flex items-start gap-2.5 text-lg">
            <IconsCheck class="mt-1.5 min-w-[20px]" />
            <p>
              <NuxtLink
                to=""
                target="_blank"
                class="text-primary-red cursor-pointer"
              >
                <span
                  class="text-primary-red cursor-pointer border-b border-black"
                  >{{ $t("watch_this_video") }}</span
                >
              </NuxtLink>
              {{ $t("for_details_on_how_to_pay") }}
            </p>
          </div>
        </div>
      </div>

      <div v-if="courseDetails.faqs.length > 0">
        <h4 class="text-[32px] font-bold text-primary-red">
          {{ $t("frequently_asked_questions") }}
        </h4>
        <div
          v-if="courseDetails?.faqs"
          class="flex flex-col mt-4 border-2 border-gray-200 px-4 py-2 rounded-lg"
        >
          <div
            v-for="(item, index) in courseDetails.faqs"
            :key="item.id"
            class="flex flex-col text-lg"
            :class="
              index === courseDetails.faqs.length - 1
                ? 'border-0'
                : 'border-b border-gray-200'
            "
          >
            <h4
              @click="toggle(item.id)"
              class="flex items-center justify-between py-2 font-semibold cursor-pointer"
            >
              <span>{{ item.title }}</span>
              <IconsCaretDown
                class="w-3 transition-all duration-300 ease-in-out"
                :class="isExpanded(item.id) ? 'rotate-180' : 'rotate-0'"
              />
            </h4>
            <div
              v-if="isExpanded(item.id)"
              class="py-2 opacity-70"
              v-html="item.description"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="courseDetails?.title"
      class="w-full lg:w-[420px] xl:w-[500px] 2xl:w-[600px]"
    >
      <div
        id="courseDetailsCard"
        class="lg:sticky lg:top-[120px] h-min rounded border-solid border-2 border-gray-200 p-4 bg-white shadow-lg"
      >
        <div v-if="courseDetails.over_view_video_url" class="bg-[#15151F]">
          <Transition name="page" mode="out-in">
            <div v-if="!overViewMedia" class="relative">
              <img
                class="w-full aspect-video z-[4]"
                :src="
                  courseDetails?.cover_image
                    ? courseDetails?.cover_image
                    : defaultThumb
                "
                alt=""
              />
              <div
                class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
              >
                <div
                  @click="overViewMedia = true"
                  class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                >
                  <IconsPlay class="w-[18px] aspect-[3/4]" />
                </div>
              </div>
            </div>
            <iframe
              v-else
              class="w-full aspect-video"
              :src="`${courseDetails.over_view_video_url}?rel=0&autoplay=1`"
              frameborder="0"
              allowfullscreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            ></iframe>
          </Transition>
        </div>

        <div
          v-else-if="
            courseDetails.banner_url && courseDetails.banner_url !== 'null'
          "
          class="w-full flex justify-center"
        >
          <img
            :src="courseDetails.banner_url"
            class="w-full aspect-auto object-contain max-w-[465px] max-h-[310px]"
            alt="courseDetails title"
          />
        </div>
        <img
          v-else
          src="/images/course-details/courseIcon.png"
          class="w-full aspect-auto object-contain"
          alt="courseDetails title"
        />
        <div class="flex items-center justify-between pt-2">
          <div v-if="courseDetails.special_price" class="flex items-center">
            <p class="text-[30px] line-through text-[#ADA7A7] font-bold">৳</p>
            <p class="text-[30px] line-through text-[#ADA7A7] mr-3">
              {{ courseDetails.price }}
            </p>
            <p class="text-[30px]">৳</p>
            <p class="text-[30px]">
              {{ courseDetails.special_price }}
            </p>
          </div>
          <div v-else class="flex">
            <p class="text-[30px]">৳</p>
            <p class="text-[30px]">
              {{ courseDetails.price }}
            </p>
          </div>
          <button
            class="w-7 h-7 rounded-full border border-gray-500 flex justify-center items-center"
            @click="showShareModal = true"
          >
            <IconsShareIt class="w-6 aspect-square" />
          </button>
          <ShareModal
            v-if="showShareModal"
            :courseUrl="courseUrl"
            :showModal="showShareModal"
            @closeModal="showShareModal = false"
          />
        </div>

        <div class="pt-6">
          <button
            class="p-3 xl:p-4 bg-black text-white w-full rounded-md text-xl font-semibold"
            @click="addToCart(courseDetails, courseDetails.id)"
          >
            {{ $t("enroll") }}
          </button>
          <!-- <button
            class="p-3 bg-black text-white hidden"
            @click="addToCart(courseDetails, courseDetails.id)"
          >
            {{ $t("add_to_cart") }}
          </button> -->
        </div>
        <div class="mt-10">
          <p class="font-bold text-[22px] pb-4 hidden">This course includes:</p>
          <ul class="flex flex-col gap-4">
            <li class="flex items-start gap-2 h-auto">
              <IconsCheck class="w-3.5 min-w-[14px] mt-1" />
              <span
                >{{ $t("total_videos") }}:
                {{ courseDetails?.total_videos }}</span
              >
            </li>
            <li class="flex items-start gap-2 h-auto">
              <IconsCheck class="w-3.5 min-w-[14px] mt-1" />
              <span
                >{{ $t("total_video_duration") }}:
                {{ courseDetails?.total_time }}</span
              >
            </li>
            <li class="flex items-start gap-2 h-auto">
              <IconsCheck class="w-3.5 min-w-[14px] mt-1" />
              <span
                >{{ $t("total_enrollment") }}:
                {{ courseDetails?.enroll_count }}</span
              >
            </li>
          </ul>
        </div>
      </div>
    </div>

    <Transition name="modal">
      <div
        v-if="showSubscriptionModal"
        class="modal-mask fixed p-4 inset-0 flex items-center justify-center z-[999999]"
      >
        <div
          class="fixed inset-0 bg-gray-600 opacity-50"
          @click="showSubscriptionModal = false"
        >
          <div class="modal-mask absolute inset-0 opacity-75"></div>
        </div>

        <div
          class="modal-container w-full md:min-w-[460px] sm:max-w-[80%] xl:w-3/5 min-h-[260px] overflow-hidden"
        >
          <IconsCross
            class="w-4 text-gray-700 absolute top-4 right-6 m-2 cursor-pointer"
            @click="showSubscriptionModal = false"
          />
          <div class="w-full h-6"></div>
          <div class="p-4 h-[calc(100vh-55px)]">
            <div class="h-full overflow-hidden">
              <h2
                class="text-3xl md:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-center text-primary-red pt-3"
              >
                {{ $t("select_a_subscription_package") }}
              </h2>
              <div
                v-if="allSubscriptions"
                class="py-5 px-4 mt-10 mx-2 md:mx-[5%] 2xl:mx-[10%] bg-white rounded-lg flex flex-col h-[calc(100%-150px)]"
                style="
                  box-shadow: 6px 6px 20px 0px rgba(0, 0, 0, 0.25),
                    -6px -6px 20px 0px rgba(0, 0, 0, 0.25);
                "
              >
                <div
                  class="md:text-xl lg:text-2xl text-center pb-2 w-5/6 self-center"
                >
                  {{ $t("its_not_late_to_make_career_decision") }}
                </div>
                <div
                  id="subscriptionModalScroll"
                  class="pt-2 h-auto overflow-x-hidden pr-4 overflow-y-auto sm:px-4 md:px-4 lg:px-[30px] rounded-lg"
                  :class="
                    scrollTop > 20 && !isScrollOnBottom
                      ? 'top-bottom-box'
                      : isScrollOnBottom && scrollTop > 0
                      ? 'top-box'
                      : 'bottom-box'
                  "
                >
                  <div
                    v-for="(item, index) in allSubscriptions"
                    :key="item?.id"
                    class="min-h-[100px] h-auto mb-5 rounded-[10px] shadow-[0px_4px_25px_-1px_rgba(0,0,0,0.25)]"
                  >
                    <label
                      :for="'radioOption-' + index"
                      @click="setSelectedRadioIndex(index, item.id)"
                      class="h-full"
                    >
                      <div
                        class="justify-between p-3 flex cursor-pointer flex-col lg:flex-row min-h-[100px]"
                        :class="{
                          'bg-[#E1F0E1] border border-solid border-1 border-green-500 rounded-[10px]':
                            item.id === selectedSubscriptionId,
                        }"
                      >
                        <div class="flex">
                          <input
                            class="mr-4 md:mr-7"
                            type="radio"
                            :id="'radioOption-' + index"
                            name="subscriptionRadioGroup"
                            :value="item.id"
                            :checked="item.id === selectedSubscriptionId"
                          />
                          <div class="text-left">
                            <h3
                              class="text-[22px] md:!text-2xl font-bold text-[#3D3B3B]"
                            >
                              {{ item.name }}
                            </h3>
                            <p
                              v-if="item?.title"
                              class="pt-1 pb-6 text-sm font-bold text-[#747474]"
                            >
                              {{ item.title }}
                            </p>
                          </div>
                        </div>

                        <div
                          class="flex font-bold pb-7 text-black pl-7 md:pl-10 md:pr-0"
                        >
                          <p class="relative flex items-center space-x-2">
                            <span class="text-xl md:!text-2xl">TK</span>
                            <span class="text-2xl md:!text-2xl">{{
                              item.amount
                            }}</span
                            ><span class="text-sm whitespace-nowrap"
                              >/{{ item.durations_as_month }}
                              {{
                                item.durations_as_month > 1 ? "months" : "month"
                              }}</span
                            >
                          </p>
                        </div>
                      </div>
                    </label>
                  </div>
                  <div class="flex justify-center mt-5 mb-8">
                    <button
                      class="w-[180px] h-9 rounded-full text-white font-bold bg-[#3D3B3B]"
                      @click="handleSubscription(selectedSubscriptionId)"
                    >
                      {{ $t("get_started") }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.modal-container {
  @apply bg-white rounded-lg shadow-xl relative border-[#00000048];
  transition: all 0.3s ease;
}

.modal-mask {
  transition: opacity 0.3s ease;
}

.modal-enter-from .modal-mask,
.modal-leave-to .modal-mask {
  opacity: 0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  opacity: 0;
  transform: scale(1.1);
  -webkit-transform: scale(1.1);
}

.has-tooltip {
  @apply relative;
}

.tooltip {
  @apply absolute text-sm bg-primary-red text-white z-[100] -left-2 -top-14 lg:-top-20 text-left invisible p-1.5 rounded-xl shadow-lg w-full min-w-[200px] whitespace-normal break-words;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}

.scroll::-webkit-scrollbar {
  width: 6px;
}

/* Handle */
.scroll::-webkit-scrollbar-thumb {
  background: red;
}

.top-box {
  box-shadow: inset 0 7px 9px -7px rgba(0, 0, 0, 0.4);
}

.bottom-box {
  box-shadow: inset 0 -7px 9px -7px rgba(0, 0, 0, 0.4);
}

.top-bottom-box {
  box-shadow: inset 0px 11px 8px -10px rgba(0, 0, 0, 0.4),
    inset 0px -11px 8px -10px rgba(0, 0, 0, 0.4);
}
</style>

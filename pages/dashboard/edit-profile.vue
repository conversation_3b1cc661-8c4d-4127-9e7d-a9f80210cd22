<script setup>
import { vMaska } from "maska";
import { ErrorMessage, Field, Form } from "vee-validate";

const props = defineProps({
  profile: {
    type: Object,
    required: false,
  },
});

const emit = defineEmits(["updateProfile"]);

const { apiBaseUrl } = useUrls();
const { isRequired, validatePassword, isWithinCharactersLimit } =
  useValidation();

const tokenCookie = useCookie("token");
const nuxtApp = useNuxtApp();
const { t } = useI18n();

const currentTab = ref("Profile");
const setCurrentTab = (tab) => {
  currentTab.value = tab;
};

const profile = ref({
  firstName: props.profile?.first_name ? props.profile?.first_name : "",
  lastName: props.profile?.last_name ? props.profile?.last_name : "",
  fullname: props.profile?.name ? props.profile?.name : "",
  username: props.profile?.username ? props.profile?.username : "",
  email: props.profile?.email ? props.profile?.email : "",
  phone: props.profile?.phone ? props.profile?.phone : "",
  occupation: props.profile?.occupations ? props.profile?.occupations : "",
  location: props.profile?.location ? props.profile?.location : "",
  bio: props.profile?.bio ? props.profile?.bio : "",
});
const isUpdating = ref(false);

watch(profile.value, () => {
  isUpdating.value = true;
});

onMounted(() => {
  setTimeout(() => {
    isUpdating.value = false;
  }, 500);
});

const password = ref({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

const profilePreview = ref(null);
const profileImage = ref(null);
const coverPreview = ref(null);
const coverImage = ref(null);

const onProfileUpload = async (event) => {
  isUpdating.value = true;
  let selectedFile = event.target.files[0];
  let reader;
  const files = event.target.files;
  if (files.length > 0) {
    reader = new FileReader();
    reader.onload = (event) => {
      profilePreview.value = event.target.result;
      profileImage.value = selectedFile;
    };
    reader.readAsDataURL(files[0]);
  }
};

const onCoverUpload = async (event) => {
  isUpdating.value = true;
  let selectedFile = event.target.files[0];
  let reader;
  const files = event.target.files;
  if (files.length > 0) {
    reader = new FileReader();
    reader.onload = (event) => {
      coverPreview.value = event.target.result;
      coverImage.value = selectedFile;
    };
    reader.readAsDataURL(files[0]);
  }
};

const submitProfileForm = async () => {
  if (
    profile.value?.phone?.length > 11 &&
    profile.value?.fullname?.length < 41 &&
    profile.value?.occupation?.length < 31
    // profile.value?.username?.length < 21
  ) {
    const {
      data: userData,
      pending,
      error,
    } = await useFetch(`${apiBaseUrl}/auth/users/${props.profile?.id}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${tokenCookie.value}`,
      },
      body: {
        name: profile.value?.fullname,
        // username: profile.value?.username,
        email: profile.value?.email,
        phone: profile.value?.phone.replace(/\s/g, ""),
        bio: profile.value?.bio,
        occupations: profile.value?.occupation,
        location: profile.value?.location,
      },
    });

    if (profileImage.value) {
      const profileFormData = new FormData();
      profileFormData.append("image", profileImage.value);
      await useFetch(
        `${apiBaseUrl}/auth/users/${props.profile?.id}/profile-image`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
          body: profileFormData,
        }
      );
    }

    if (coverImage.value) {
      const coverFormData = new FormData();
      coverFormData.append("cover_image", coverImage.value);
      await useFetch(
        `${apiBaseUrl}/auth/users/${props.profile?.id}/cover-image`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
          body: coverFormData,
        }
      );
    }

    const setData = () => {
      if (!pending.value) {
        if (userData.value) {
          if (userData.value.user) {
            isUpdating.value = false;
            nuxtApp.$toast("clear");
            nuxtApp.$toast("success", {
              message: t("messages.profile_updated"),
              className: "toasted-bg-success",
            });
          }
          emit("updateProfile");
        } else if (error.value) {
          isUpdating.value = false;
          nuxtApp.$toast("clear");
          nuxtApp.$toast("error", {
            message: error.value?.data.message.username
              ? error.value?.data.message.username
              : error.value?.data.message.phone
              ? error.value?.data.message.phone
              : t("messages.something_wrong"),
            className: "toasted-bg-alert",
          });
        }
      } else {
        setTimeout(() => {
          setData();
        }, 300);
      }
    };
    setData();
  } else {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message:
        profile.value.phone === ""
          ? t("messages.phone_field_required")
          : t("messages.some_field_not_valid"),
      className: "toasted-bg-alert",
    });
    isUpdating.value = false;
  }
};

const passwordForm = ref(null);

const submitPasswordForm = async () => {
  if (password.value?.currentPassword === password.value?.newPassword) {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: t("password_matching"),
      className: "toasted-bg-error",
    });
    return;
  }
  const { data, error } = await useFetch(
    `${apiBaseUrl}/auth/users/${props.profile?.id}/password`,
    {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${tokenCookie.value}`,
      },
      body: {
        password: password.value?.newPassword,
        password_confirmation: password.value?.confirmPassword,
        current_password: password.value?.currentPassword,
      },
    }
  );
  if (data.value && data.value.user) {
    nuxtApp.$toast("clear");
    nuxtApp.$toast("success", {
      message: t("messages.password_updated"),
      className: "toasted-bg-success",
    });
  } else {
    if (error.value.statusCode === 422) {
      passwordForm.value?.setErrors(error.value.data.errors);
    } else {
      nuxtApp.$toast("clear");
      nuxtApp.$toast("error", {
        message: `${error.value.data.message}`,
        className: "toasted-bg-alert",
      });
    }
  }
};
</script>

<template>
  <div class="py-5 md:py-20">
    <div
      class="flex flex-row items-start text-base sm:!text-2xl font-semibold text-center pb-2 whitespace-nowrap w-full lg:!w-[80%] xl:!w-[60%]"
    >
      <p
        class="border-b-[3px] pb-4 w-1/3"
        :class="
          currentTab === 'Profile' ? 'border-primary-red' : 'border-[#9C9C9C]'
        "
      >
        <span
          class="cursor-pointer px-2 py-2 hover:text-primary-red"
          @click="setCurrentTab('Profile')"
          >{{ $t("profile") }}</span
        >
      </p>
      <p
        class="border-b-[3px] pb-4 w-1/3"
        :class="
          currentTab === 'Password' ? 'border-primary-red' : 'border-[#9C9C9C]'
        "
      >
        <span
          class="cursor-pointer px-2 py-2 hover:text-primary-red"
          @click="setCurrentTab('Password')"
          >{{ $t("password") }}</span
        >
      </p>
    </div>
    <Transition name="fade" mode="out-in">
      <div v-if="currentTab === 'Profile'" class="">
        <div class="pt-5">
          <div
            class="relative bg-cover bg-top bg-[#363b64] h-[280px] lg:!h-[300px] xl:!h-[350px] w-full"
            :style="
              coverPreview
                ? { backgroundImage: `url('${coverPreview}')` }
                : { backgroundImage: `url('${props.profile?.cover_image}')` }
            "
          >
            <div
              class="hidden cursor-pointer w-8 h-8 xl:!h-10 xl:!w-10 absolute top-5 right-5 border-2 border-black rounded-full flex justify-center items-center"
            >
              <IconsTrash class="w-4 xl:!w-5 cursor-pointer" />
            </div>
            <div
              class="bg-[#D9D9D9] h-28 w-28 xl:!w-40 xl:!h-40 absolute !-bottom-4 lg:!-bottom-7 md:!left-10 left-0 right-0 mx-auto md:!mx-0 border-white border-[3px] rounded-full flex justify-center items-center overflow-hidden"
            >
              <img
                v-if="!profilePreview && !props.profile?.image"
                class="h-28 w-28 xl:!w-40 xl:!h-40 object-cover object-top"
                src="/images/dashboard/user.png"
                :alt="profile.name"
              />
              <img
                v-else
                class="h-28 w-28 xl:!w-40 xl:!h-40 object-cover object-top"
                :src="profilePreview ? profilePreview : props.profile?.image"
                :alt="profile.name"
              />

              <IconsPencil
                class="absolute left-0 right-0 mx-auto bottom-6 cursor-pointer w-4"
                @click.stop="$refs.profileInput.click()"
              />
              <input
                ref="profileInput"
                style="display: none"
                type="file"
                accept="image/*"
                @change="onProfileUpload"
              />
            </div>
            <div>
              <div class="flex flex-col items-center pt-8 md:!hidden">
                <div
                  class="w-[60px] h-[60px] cursor-pointer"
                  @click.stop="$refs.coverInput.click()"
                >
                  <IconsImage class="min-w-full" />
                </div>
                <span class="text-base text-[#0000001e] font-bold"
                  >335x230</span
                >
              </div>
              <div
                class="hidden absolute bottom-5 right-5 bg-primary-red md:!flex items-center space-x-4 h-[50px] px-5 cursor-pointer"
                @click.stop="$refs.coverInput.click()"
              >
                <IconsImage class="w-6" />
                <span class="text-base text-white font-bold">{{
                  $t("upload_cover_photo")
                }}</span>
              </div>
              <input
                ref="coverInput"
                style="display: none"
                type="file"
                accept="image/*"
                @change="onCoverUpload"
              />
            </div>
          </div>
          <div class="md:flex justify-between items-center pt-3.5 hidden">
            <span></span>
            <div class="flex items-center text-sm space-x-2.5">
              <IconsWarning />
              <p class="pr-2.5">
                {{ $t("profile_photo_size") }} :
                <span class="font-semibold">200x200</span> {{ $t("pixels") }}
              </p>
              <p>
                {{ $t("cover_photo_size") }} :
                <span class="font-semibold">955x350</span> {{ $t("pixels") }}
              </p>
            </div>
          </div>
        </div>
        <Form
          @submit="submitProfileForm"
          @keypress.enter="submitProfileForm"
          v-slot="{ meta }"
          class="pt-20"
        >
          <div class="flex flex-col space-y-5">
            <div
              class="flex flex-col md:!flex-row md:!space-x-5 space-y-5 md:!space-y-0 w-full"
            >
              <div class="flex flex-col space-y-4 w-full">
                <label
                  for="fullname"
                  class="text-sm font-semibold text-primary-red"
                  >{{ $t("full_name") }}*</label
                >
                <Field
                  v-model="profile.fullname"
                  id="fullname"
                  name="fullname"
                  class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                  type="text"
                  :placeholder="$t('full_name')"
                  :rules="isRequired"
                />
                <div class="!mt-1 ml-1 text-primary-red">
                  {{ isWithinCharactersLimit(profile.fullname, 40) }}
                </div>
                <ErrorMessage class="error-message" name="fullname" />
              </div>
            </div>
            <div class="flex flex-col w-full">
              <!-- <div class="hidden flex-col space-y-4 w-full md:!w-1/2">
                <label
                  for="username"
                  class="text-sm font-semibold text-primary-red"
                  >{{ $t("user_name") }}*</label
                >
                <Field
                  v-model="profile.username"
                  id="username"
                  name="username"
                  class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                  type="text"
                  :placeholder="$t('user_name')"
                  :rules="isRequired"
                />
                <div class="!mt-1 ml-1 text-primary-red">
                  {{ isWithinCharactersLimit(profile.username, 20) }}
                </div>
                <ErrorMessage class="error-message" name="username" />
              </div> -->
              <div class="flex flex-col space-y-4 w-full">
                <label
                  for="phone"
                  class="text-sm font-semibold text-primary-red"
                  >{{ $t("phone") }}*</label
                >
                <Field
                  v-model="profile.phone"
                  id="phone"
                  name="phone"
                  class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                  type="text"
                  v-maska
                  data-maska="+### #### #### ####"
                  :rules="isRequired"
                  :placeholder="$t('phone')"
                />
                <ErrorMessage class="error-message" name="phone" />
              </div>
            </div>
            <div
              class="flex flex-col md:!flex-row md:!space-x-5 space-y-5 md:!space-y-0 w-full"
            >
              <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                <label for="occupation" class="text-sm font-semibold">{{
                  $t("occupation")
                }}</label>
                <Field
                  v-model="profile.occupation"
                  id="occupation"
                  name="occupation"
                  class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                  type="text"
                  :placeholder="$t('occupation')"
                />
                <div class="!mt-1 ml-1 text-primary-red">
                  {{ isWithinCharactersLimit(profile.occupation, 30) }}
                </div>
              </div>
              <div class="flex flex-col space-y-4 w-full md:!w-1/2">
                <label for="location" class="text-sm font-semibold">{{
                  $t("location")
                }}</label>
                <Field
                  v-model="profile.location"
                  id="location"
                  name="location"
                  class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                  type="text"
                  :placeholder="$t('location')"
                />
              </div>
            </div>

            <div class="flex flex-col space-y-4 w-full">
              <label for="bio" class="text-sm font-semibold">{{
                $t("biography")
              }}</label>
              <Field
                v-model="profile.bio"
                as="textarea"
                id="bio"
                name="bio"
                class="border border-[#9C9C9C] rounded-md px-3 py-2 outline-none"
                type="text"
                rows="10"
                :placeholder="$t('biography')"
              ></Field>
            </div>
            <button
              class="outline-none disabled:opacity-70 !bg-primary-red !text-white !font-semibold !py-3 !rounded-sm !w-40 !mt-20"
              type="submit"
              :disabled="!meta.valid || !isUpdating"
            >
              {{ $t("update_profile") }}
            </button>
          </div>
        </Form>
      </div>
      <div v-else-if="currentTab === 'Password'" class="pt-5">
        <Form
          ref="passwordForm"
          @submit="submitPasswordForm"
          @keypress.enter="submitPasswordForm"
          v-slot="{ meta }"
        >
          <div class="flex flex-col space-y-5 w-full lg:!w-[80%] xl:!w-[60%]">
            <div class="flex flex-col space-y-4 w-full">
              <label for="current_password" class="text-sm font-semibold">{{
                $t("current_password")
              }}</label>
              <Field
                v-model="password.currentPassword"
                id="current_password"
                name="current_password"
                class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                type="password"
                :placeholder="$t('current_password')"
                :rules="validatePassword"
              />
              <ErrorMessage class="error-message" name="current_password" />
            </div>
            <div class="flex flex-col space-y-4 w-full">
              <label for="password" class="text-sm font-semibold">{{
                $t("new_password")
              }}</label>
              <Field
                v-model="password.newPassword"
                id="password"
                name="password"
                class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                type="password"
                :placeholder="$t('new_password')"
                :rules="validatePassword"
              />
              <ErrorMessage class="error-message" name="password" />
            </div>
            <div class="flex flex-col space-y-4 w-full">
              <label
                for="password_confirmation"
                class="text-sm font-semibold"
                >{{ $t("confirm_new_password") }}</label
              >
              <Field
                v-model="password.confirmPassword"
                id="password_confirmation"
                name="password_confirmation"
                class="border border-[#9C9C9C] rounded-md px-3 py-3 outline-none"
                type="password"
                :placeholder="$t('confirm_new_password')"
                :rules="validatePassword"
              />
              <ErrorMessage
                class="error-message"
                name="password_confirmation"
              />
            </div>
            <button
              class="outline-none !bg-primary-red !text-white !font-semibold !py-3 !rounded-sm !w-40 !mt-20 disabled:opacity-70"
              type="submit"
              :disabled="!meta.valid"
            >
              {{ $t("reset_password") }}
            </button>
          </div>
        </Form>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.input-wrapper {
  @apply flex flex-col md:!flex-row items-start md:!items-center w-full space-y-3 md:!space-y-0;
}
</style>

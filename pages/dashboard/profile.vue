<script setup>
defineProps({
  profile: {
    type: Object,
    required: false,
  },
});
const localePath = useLocalePath();
</script>

<template>
  <div
    v-if="profile"
    class="mt-10 bg-[#F8F8F8] rounded-[20px] pb-20 overflow-hidden"
  >
    <div
      class="bg-cover bg-top bg-[#363b64] h-[280px] lg:!h-[300px] xl:!h-[350px] w-full rounded-[20px] relative"
      :style="
        profile.cover_image
          ? { backgroundImage: `url('${profile.cover_image}')` }
          : '/images/dashboard/cover.png'
      "
    >
      <div
        class="w-28 h-28 lg:!w-40 lg:!h-40 absolute !-bottom-8 md:!left-5 rounded-[20px] left-0 right-0 mx-auto md:!mx-0"
      >
        <img
          v-if="profile.image"
          class="w-28 h-28 lg:!w-40 lg:!h-40 object-cover object-top rounded-[20px]"
          :src="profile.image"
          :alt="profile.name"
        />
        <div v-else>
          <ClientOnly>
            <fa
              class="w-28 h-28 lg:!w-40 lg:!h-40 object-cover object-top rounded-[20px]"
              :icon="['fa-solid', 'user']"
          /></ClientOnly>
        </div>
      </div>
    </div>
    <div class="flex justify-between items-center pt-8">
      <span></span>
      <!-- <IconsThreeDots class="mr-5 cursor-pointer" /> -->
      <NuxtLink :to="localePath('/dashboard/edit-profile')">
        <button
          class="text-xs px-3 pt-1.5 pb-1 bg-primary-red text-white rounded-md mr-5"
        >
          {{ $t("edit_profile") }}
        </button>
      </NuxtLink>
    </div>
    <div class="p-5 flex flex-col md:!flex-row space-x-0 md:!space-x-2">
      <div
        class="flex flex-col text-lg w-full md:!w-[30%] items-center md:!items-start"
      >
        <h3 class="text-[32px] font-bold">{{ profile.name }}</h3>
        <p class="font-semibold pt-1 pb-2 md:!pt-[18px] md:!pb-[22px]">
          {{ profile.occupations }}
        </p>
        <p class="flex items-center space-x-2">
          <IconsLocation /><span>{{ profile.location }}</span>
        </p>
        <p class="flex items-center space-x-2 mt-3">
          <img
            class="h-[18px] aspect-square"
            src="/icons/time-zone-icon.webp"
            alt=""
          />
          <span class="ml-2">{{ profile.timezone }}</span>
        </p>
      </div>
      <div
        class="flex flex-col w-full md:!w-[70%] text-lg pt-12 space-y-6 md:!space-y-[30px]"
      >
        <div
          class="flex flex-col md:!flex-row items-start justify-between space-x-0 md:!space-x-2 space-y-6 md:!space-y-0"
        >
          <div class="flex flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2">
            <h4>{{ $t("phone") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper">
                <IconsPhone />
              </p>
              <span>{{ profile.phone }}</span>
            </div>
          </div>
          <div class="flex flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2">
            <h4>{{ $t("email") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper">
                <IconsEmail />
              </p>
              <span>{{ profile.email }}</span>
            </div>
          </div>
        </div>
        <div
          class="flex flex-col md:!flex-row items-start justify-between space-x-0 md:!space-x-2 space-y-6 md:!space-y-0"
        >
          <div class="flex flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2">
            <h4>{{ $t("registration_date") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper">
                <IconsRegistration />
              </p>
              <span>
                {{ $dayjs(profile.created_at).format("DD MMM, YYYY h:mm a") }}
              </span>
            </div>
          </div>
          <div
            class="hidden flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2"
          >
            <h4>{{ $t("user_name") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper text-white p-2">
                <ClientOnly>
                  <fa class="" :icon="['fa-solid', 'user']"
                /></ClientOnly>
              </p>
              <span>{{ profile.username }}</span>
            </div>
          </div>
          <div class="flex flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2">
            <h4>{{ $t("occupation") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper text-white p-2">
                <ClientOnly>
                  <fa class="" :icon="['fa-solid', 'briefcase']"
                /></ClientOnly>
              </p>
              <span>{{ profile.occupations }}</span>
            </div>
          </div>
        </div>
        <div
          class="flex flex-col md:!flex-row items-start justify-between space-x-0 md:!space-x-2 space-y-6 md:!space-y-0"
        >
          <div class="flex flex-col space-y-2.5 md:!space-y-4 w-full md:!w-1/2">
            <h4>{{ $t("biography") }}</h4>
            <div class="field-wrapper">
              <p class="icon-wrapper text-white p-2">
                <ClientOnly>
                  <fa class="" :icon="['fa-solid', 'dna']"
                /></ClientOnly>
              </p>
              <span>{{ profile.bio }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.cover {
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #363b64;
}
.icon-wrapper {
  @apply h-10 w-10 min-w-[40px] bg-[#FE9874] rounded-[10px] flex justify-center items-center;
}
.field-wrapper {
  @apply flex items-start font-semibold space-x-3;
}
</style>

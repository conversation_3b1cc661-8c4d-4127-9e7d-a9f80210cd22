<script setup>
import debounce from "lodash.debounce";

// Composable
const { isLargerOrEqual } = useBreakpoints();
const config = useRuntimeConfig();
const { BLOGS } = useUrls();
const route = useRoute();
const localePath = useLocalePath();
const { t } = useI18n();
// State
const isDesktop = computed(() => isLargerOrEqual(1024));
const searchText = ref("");
// Functions
const setSearchParams = () => {
  if (searchText.value.length > 0) {
    navigateTo(localePath(`/blog?search=${searchText.value}`));
  }
};

const { data: singleBlogRes } = await useFetch(`${BLOGS}/${route.params.slug}`);
const singleBlog = computed(() => singleBlogRes.value?.data);
const singleBlogData = computed(() => singleBlogRes.value);

useSeoMeta({
  title: () =>
    singleBlogData.value
      ? singleBlogData.value?.data?.title + " | " + t("site_name")
      : t("page_not_found"),
  ogTitle: () =>
    singleBlogData.value
      ? singleBlogData.value?.data?.title + " | " + t("site_name")
      : t("page_not_found"),
  description: () =>
    singleBlogData.value ? singleBlogData.value?.data?.short_description : "",
  ogDescription: () =>
    singleBlogData.value ? singleBlogData.value?.data?.short_description : "",
  ogImage: () =>
    singleBlogData.value ? singleBlogData.value?.data?.image : "",
  twitterCard: "summary_large_image",
});

const { data: relatedPostsRes } = await useFetch(
  `${BLOGS}/${route.params.slug}/related-blogs`
);
const relatedPosts = computed(() => relatedPostsRes.value?.data);

const postUrl = computed(() => {
  return `${config.public.siteUrl}blog/${singleBlog.value.slug}`;
});

// watchers
watch(
  () => searchText.value,
  debounce(() => {
    setSearchParams();
  }, 500)
);

onMounted(() => {});
</script>

<template>
  <div class="custom-container pt-[30px] pb-20 min-h-screen">
    <Transition name="fade" mode="out-in">
      <div v-if="singleBlog">
        <div class="flex items-start space-x-4 text-xl">
          <NuxtLink :to="localePath('/blog')">
            <span class="text-[#656565]">{{ $t("blog") }}</span>
          </NuxtLink>
          <span> - </span>
          <h1 class="text-primary-red">{{ singleBlog?.title }}</h1>
        </div>
        <div
          class="pt-10 lg:pt-16 flex flex-col lg:flex-row gap-[30px] xl:gap-10 h-full"
        >
          <div class="w-full h-full">
            <div class="w-full h-full bg-[#F8F8F8] pb-10">
              <div class="">
                <img
                  class="object-cover w-full aspect-video"
                  :src="singleBlog?.image"
                  :alt="singleBlog?.title"
                />
                <div
                  class="flex flex-wrap items-center gap-3 xl:gap-5 text-[#505050] text-2xl py-8 mx-[30px]"
                >
                  <div
                    class="flex items-center gap-3 pr-5 border-r border-[#808080]"
                  >
                    <NuxtLink
                      :to="
                        localePath(
                          `/blog?category=${singleBlog?.blogCategory?.slug}`
                        )
                      "
                    >
                      <span class="hover:text-[#ec1f27]">{{
                        singleBlog?.blogCategory?.title
                      }}</span>
                    </NuxtLink>
                  </div>
                  <p
                    class="flex items-center text-[#808080] gap-3 pr-5 border-r border-[#808080]"
                  >
                    <IconsCalender class="text-[#EC1F27] w-5 h-5" />
                    <span class="">{{
                      $dateFormat(singleBlog?.created_at)
                    }}</span>
                  </p>
                  <p class="flex items-center text-[#808080] gap-2">
                    <span class="">By - </span>
                    <span class="">{{ singleBlog?.created_by?.name }}</span>
                  </p>
                  <div v-if="false" class="flex items-center space-x-2.5">
                    <IconsShare class="w-3 h-3 mr-1" />
                    <NuxtLink
                      :to="`https://www.facebook.com/share.php?u=${postUrl}`"
                      target="_blank"
                    >
                      <SvgFacebook class="w-8 h-8" />
                    </NuxtLink>
                    <NuxtLink
                      :to="`http://twitter.com/share?&url=${postUrl}&text=Sk Mobile School blog about - ${singleBlog.title}.`"
                      target="_blank"
                    >
                      <SvgTwitter class="w-8 h-8" />
                    </NuxtLink>
                    <NuxtLink
                      :to="`https://www.linkedin.com/sharing/share-offsite/?url=${postUrl}`"
                      target="_blank"
                    >
                      <SvgLinkedin class="w-8 h-8" />
                    </NuxtLink>
                    <!-- <NuxtLink
                      :to="`instagram-stories://share?source=${postUrl}`"
                      target="_blank"
                    >
                      <SvgInstagram class="w-8 h-8" />
                    </NuxtLink> -->
                    <NuxtLink
                      :to="`mailto:?subject=${singleBlog.title}&body=${postUrl}`"
                      target="_blank"
                    >
                      <SvgGmail class="w-8 h-8" />
                    </NuxtLink>
                  </div>
                </div>
              </div>
              <div v-html="singleBlog?.content" class="mx-[30px] pb-10"></div>
              <div
                class="mx-[30px] flex flex-wrap gap-5 items-center justify-end text-[#656565] text-xl pb-4 border-b border-[#8E8E8E]"
              >
                <div class="items-center gap-4 sm:gap-6 hidden">
                  <div class="flex items-center gap-3">
                    <IconsLike class="w-10 h-10 bg-white rounded-full" />
                    <p class="">14 Likes</p>
                  </div>
                  <div class="flex items-center gap-3">
                    <IconsComments class="w-10 h-10 bg-white rounded-full" />
                    <p class="">5 Comments</p>
                  </div>
                </div>
                <div class="flex items-center gap-2 sm:gap-4">
                  <IconsShare class="w-5 h-auto mr-2" />
                  <NuxtLink
                    :to="`https://www.facebook.com/share.php?u=${postUrl}`"
                    target="_blank"
                  >
                    <SvgFacebook class="w-8 h-8" />
                  </NuxtLink>
                  <NuxtLink
                    :to="`http://twitter.com/share?&url=${postUrl}&text=Sk Mobile School blog about - ${singleBlog.title}.`"
                    target="_blank"
                  >
                    <SvgTwitter class="w-8 h-8" />
                  </NuxtLink>
                  <NuxtLink
                    :to="`https://www.linkedin.com/sharing/share-offsite/?url=${postUrl}`"
                    target="_blank"
                  >
                    <SvgLinkedin class="w-8 h-8" />
                  </NuxtLink>
                  <!-- <NuxtLink
                      :to="`instagram-stories://share?source=${postUrl}`"
                      target="_blank"
                    >
                      <SvgInstagram class="w-8 h-8" />
                    </NuxtLink> -->
                  <NuxtLink
                    :to="`mailto:?subject=${singleBlog.title}&body=${postUrl}`"
                    target="_blank"
                  >
                    <SvgGmail class="w-8 h-8" />
                  </NuxtLink>
                </div>
              </div>
              <BlogComments v-if="false" class="hidden" />
              <BlogCommentsForm v-if="false" class="hidden" />
            </div>
            <div
              v-if="!isDesktop"
              class="mt-10 w-full px-[30px] pt-5 pb-8 xl:pb-12 bg-[#F8F8F8] rounded-md shadow-[2px_2px_6px_#65656527]"
            >
              <h2 class="text-xl xl:text-[28px] text-[#505050] font-bold">
                {{ $t("related_posts") }}:
              </h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-5 pt-[30px]">
                <BlogCard
                  v-for="blog in relatedPosts"
                  :key="blog.id"
                  :post="blog"
                />
              </div>
            </div>
          </div>
          <div
            class="w-full lg:min-w-[320px] lg:w-[320px] xl:w-[400px] xl:min-w-[400px] h-full space-y-10"
          >
            <div
              class="w-full h-[50px] bg-[#F8F8F8] text-xl flex items-center rounded-full px-4 shadow-[2px_2px_6px_#6565654D]"
            >
              <IconsSearch class="w-6 mr-2.5 text-[#656565]" />
              <input
                class="w-full h-full outline-none rounded-r-full text-[#505050] bg-[#F8F8F8]"
                type="text"
                :placeholder="$t('search')"
                v-model="searchText"
              />
            </div>

            <BlogCategories />
            <BlogTopPosts v-if="false" class="hidden" />
            <BlogTags v-if="false" class="hidden" />

            <div
              v-if="isDesktop"
              class="block w-full px-[30px] pt-5 pb-8 xl:pb-12 bg-[#F8F8F8] rounded-md shadow-[2px_2px_6px_#65656527]"
            >
              <h2 class="text-xl xl:text-[28px] text-[#505050] font-bold">
                {{ $t("related_posts") }}:
              </h2>
              <div class="grid grid-cols-1 gap-5 pt-[30px]">
                <BlogCard
                  v-for="blog in relatedPosts"
                  :key="blog.id"
                  :post="blog"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="flex justify-center items-center pt-20">
        <span class="text-xl md:text-3xl xl:text-5xl">{{
          pending ? $t("loading") : $t("page_not_found")
        }}</span>
      </div>
    </Transition>
  </div>
</template>

<style scoped></style>

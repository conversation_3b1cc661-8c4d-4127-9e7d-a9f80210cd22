<script setup>
import debounce from "lodash.debounce";
import { Carousel, Navigation, Pagination, Slide } from "vue3-carousel";
import "vue3-carousel/dist/carousel.css";

useHead({
  title: "SK Mobile School Blogs",
});

// Composables
const { isSmaller, isLargerOrEqual, isBetween } = useBreakpoints();
const { BLOGS } = useUrls();
const route = useRoute();
const nuxtApp = useNuxtApp();
const localePath = useLocalePath();
// States
const isMobile = computed(() => isSmaller(768));
const isSmallMobile = computed(() => isSmaller(640));
const isMediumMobile = computed(() => isBetween(640, 1280));
const isDesktop = computed(() => isLargerOrEqual(1280));
const blogs = ref([]);
const currentPage = ref(1);
const totalPages = ref(1);
const totalBlogs = ref(1);
const perPageBlogs = ref(9);
const isLoading = ref(false);
const searchText = ref("");
const blogsRef = ref(null);
const perPagePaginationButton = computed(() => {
  if (isSmallMobile.value) {
    return 4;
  } else if (isMediumMobile.value) {
    return 5;
  } else if (isDesktop.value) {
    return 10;
  }
});
const query = computed(() => route.query);
const visiblePageNumbers = computed(() => {
  const startPage = Math.max(
    1,
    currentPage.value - Math.floor(perPagePaginationButton.value / 2)
  );
  const endPage = Math.min(
    totalPages.value,
    startPage + perPagePaginationButton.value - 1
  );

  return Array.from(
    { length: endPage - startPage + 1 },
    (_, index) => startPage + index
  );
});
// Methods
const { data: featuredPostsRes } = await useFetch(`${BLOGS}/sliders`);
const featuredPosts = computed(() => featuredPostsRes.value?.data);
const setSearchParams = () => {
  if (searchText.value.length > 0) {
    navigateTo(localePath(`/blog?search=${searchText.value}`));
  } else {
    navigateTo(localePath(`/blog`));
  }
};
const fetchBlogs = async (query) => {
  try {
    isLoading.value = true;
    let res = {};
    if (query) {
      if (query?.page) {
        if (query?.category) {
          res = await $fetch(
            `${BLOGS}?per_page=${perPageBlogs.value}&slug=${query.category}&page=${query.page}`
          );
        } else if (query?.search) {
          searchText.value = query.search;
          res = await $fetch(
            `${BLOGS}?per_page=${perPageBlogs.value}&query=${query.search}&page=${query.page}`
          );
        } else {
          res = await $fetch(
            `${BLOGS}?per_page=${perPageBlogs.value}&page=${query.page}`
          );
        }
      } else if (query?.category) {
        res = await $fetch(
          `${BLOGS}?per_page=${perPageBlogs.value}&slug=${query.category}`
        );
      } else if (query?.search) {
        searchText.value = query.search;
        res = await $fetch(
          `${BLOGS}?per_page=${perPageBlogs.value}&query=${query.search}`
        );
      } else {
        blogs.value = {};
        currentPage.value = 0;
        totalPages.value = 0;
        perPageBlogs.value = 0;
        totalBlogs.value = 0;
        return;
      }
    } else {
      res = await $fetch(`${BLOGS}?per_page=${perPageBlogs.value}`);
      searchText.value = "";
    }
    blogs.value = res.data;
    currentPage.value = res.meta.current_page;
    totalPages.value = res.meta.last_page;
    perPageBlogs.value = res.meta.per_page;
    totalBlogs.value = res.meta.total;
    isLoading.value = false;
  } catch (error) {
    console.log(error);
    isLoading.value = false;
  } finally {
    isLoading.value = false;
  }
};
const setPageRoute = (page) => {
  if (query.value?.category) {
    navigateTo(
      localePath(`/blog?category=${query.value.category}&page=${page}`)
    );
  } else if (query.value?.search) {
    navigateTo(localePath(`/blog?search=${query.value.search}&page=${page}`));
  } else {
    navigateTo(localePath(`/blog?page=${page}`));
  }
  window.scrollTo(0, blogsRef.value.offsetTop - 100);
};
const goToPrevPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    setPageRoute(currentPage.value);
  }
};
const goToNextPage = async () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    setPageRoute(currentPage.value);
  }
};
const gotToPage = async (page) => {
  if (currentPage.value !== page) {
    setPageRoute(page);
  }
};
const isCurrentPage = (page) => {
  return currentPage.value === page;
};
const isPrevBtnDisabled = computed(() => {
  return currentPage.value <= 1;
});
const isNextBtnDisabled = computed(() => {
  return currentPage.value >= totalPages.value;
});
// Watchers
watch(
  () => route.query,
  async (query) => {
    if (Object.keys(query).length === 0) {
      await fetchBlogs(null);
    } else {
      await fetchBlogs(query);
    }
  }
);
watch(
  () => searchText.value,
  debounce(() => {
    setSearchParams();
  }, 500)
);
onMounted(async () => {
  await nextTick();
  window.scrollTo(0, 0);
});
// Created
await fetchBlogs(Object.keys(route.query).length === 0 ? null : route.query);
</script>

<template>
  <div class="custom-container pt-[30px]">
    <div class="">
      <div class="text-center font-bold space-y-2.5 xl:space-y-5">
        <p class="text-2xl lg:text-4xl text-primary-red">
          {{ $t("tool_shop") }}
        </p>
        <h1 class="text-4xl lg:text-[40px] xl:text-[50px] text-black">
          {{ $t("blog_title") }}
        </h1>
      </div>
      <div v-if="featuredPosts" class="pt-9 xl:pt-[46px] mx-0 md:mx-5 lg:mx-0">
        <carousel
          v-if="featuredPosts.length > 0"
          class="blog-carousel"
          :items-to-show="1"
          :transition="500"
          :autoplay="2000"
          :pauseAutoplayOnHover="true"
          napAlign="center"
        >
          <slide v-for="slide in featuredPosts" :key="slide.id" class="p-2">
            <div
              class="featured h-auto custom-height lg:h-[366px] xl:h-[480px] 3xl:h-[560px] flex flex-col lg:flex-row items-start justify-between gap-6 xl:gap-10"
            >
              <img
                class="w-full lg:w-1/2 h-[240px] sm:h-[350px] lg:h-full rounded-2xl object-cover"
                :src="slide.image"
                :alt="slide.title"
              />
              <div class="flex flex-col justify-between w-full lg:w-1/2 h-full">
                <div class="text-[#222831] text-left">
                  <h2
                    class="text-xl lg:text-3xl xl:text-4xl font-bold xl:leading-[50px] line-clamp-2"
                  >
                    {{ slide.title }}
                  </h2>
                  <div
                    class="flex items-center text-[#656565] gap-3 text-base lg:text-lg xl:text-xl pt-2 xl:pt-4 pb-3 xl:pb-6"
                  >
                    <div class="border-r border-r-[#656565] pr-3">
                      <NuxtLink
                        :to="
                          localePath(`/blog?category=${slide.blogCategorySlug}`)
                        "
                        class="hover:text-[#ec1f27]"
                        >{{ slide.blogCategoryTitle }}</NuxtLink
                      >
                    </div>
                    <p class="flex items-center text-[#808080] space-x-2">
                      <IconsCalender class="text-[#EC1F27] w-5 h-5 pb-0.5" />
                      <span>{{ $dateFormat(slide.created_at) }}</span>
                    </p>
                  </div>
                  <p
                    class="text-base lg:text-lg xl:text-[28px] text-[#222831] xl:leading-9 line-clamp-5 3xl:line-clamp-6"
                  >
                    {{ slide.short_description ? slide.short_description : "" }}
                  </p>
                </div>
                <div
                  class="w-full flex justify-center items-center sm:justify-start pt-6 xl:pt-10"
                >
                  <NuxtLink
                    :to="localePath(`/blog/${slide.slug}`)"
                    class="w-[180px] xl:w-[220px] h-10 xl:h-[50px] rounded-full text-white font-bold bg-primary-red flex justify-center items-center"
                  >
                    <span>{{ $t("continue_reading_btn") }}</span>
                  </NuxtLink>
                </div>
              </div>
            </div>
          </slide>

          <template #addons>
            <navigation v-if="!isMobile" class="blog-navigation" />
            <pagination class="blog-pagination" />
          </template>
        </carousel>
        <NoPageFound v-else minHeight="300" />
      </div>
    </div>

    <div
      ref="blogsRef"
      class="pt-12 pb-[86px] flex flex-col-reverse lg:flex-row gap-8 h-full"
    >
      <Transition name="fade" mode="out-in">
        <div
          v-if="isLoading"
          class="w-full h-full flex justify-center items-center pt-20"
        >
          <span class="text-xl md:text-3xl xl:text-5xl">Loading...</span>
        </div>
        <div
          v-else-if="!isLoading && blogs.length > 0"
          class="pt-4 lg:pt-0 grid grid-cols-1 sm:grid-cols-2 2dx:grid-cols-3 gap-8 w-full h-full"
        >
          <BlogCard v-for="blog in blogs" :key="blog.id" :post="blog" />
        </div>
        <div
          v-else
          class="w-full h-full flex justify-center items-center pt-20"
        >
          <span class="text-xl md:text-3xl xl:text-5xl">{{
            $t("no_data_found")
          }}</span>
        </div>
      </Transition>
      <div
        class="w-full lg:min-w-[320px] lg:w-[320px] xl:w-[400px] xl:min-w-[400px] h-full space-y-10"
      >
        <div
          class="w-full h-[50px] bg-[#F8F8F8] text-xl flex items-center rounded-full px-4 shadow-[2px_2px_6px_#6565654D]"
        >
          <IconsSearch class="w-6 mr-2.5 text-[#656565]" />
          <input
            class="w-full h-full outline-none rounded-r-full text-[#505050] bg-[#F8F8F8]"
            type="text"
            :placeholder="$t('search')"
            v-model="searchText"
          />
        </div>

        <BlogCategories />
        <BlogTopPosts class="hidden" />
        <BlogTags class="hidden" />
      </div>
    </div>
    <!-- Pagination  -->
    <div
      v-if="isSmallMobile && totalBlogs > perPageBlogs && blogs.length > 0"
      class="w-full flex items-center pb-6 space-x-4"
    >
      <button
        @click="goToPrevPage"
        :disabled="isPrevBtnDisabled"
        class="pagination-btn inactive-btn !w-1/2"
        :class="{ 'opacity-50': isPrevBtnDisabled }"
      >
        <IconsCaretDown class="w-4 md:w-6 rotate-90" />
      </button>
      <button
        @click="goToNextPage"
        :disabled="isNextBtnDisabled"
        class="pagination-btn inactive-btn !w-1/2"
        :class="{ 'opacity-50': isNextBtnDisabled }"
      >
        <IconsCaretDown class="w-4 md:w-6 -rotate-90" />
      </button>
    </div>
    <div
      v-if="totalBlogs > perPageBlogs && blogs.length > 0"
      class="flex justify-center items-center space-x-3.5 xl:space-x-5 pb-[70px]"
    >
      <button
        v-if="!isSmallMobile"
        @click="goToPrevPage"
        :disabled="isPrevBtnDisabled"
        class="pagination-btn inactive-btn"
        :class="{ 'opacity-50': isPrevBtnDisabled }"
      >
        <IconsCaretDown class="w-4 md:w-6 rotate-90" />
      </button>
      <button
        :disabled="isPrevBtnDisabled"
        @click="gotToPage(1)"
        class="pagination-btn inactive-btn"
        :class="{ 'opacity-50': isPrevBtnDisabled }"
      >
        <IconsAnglesRight class="w-4 md:w-6 rotate-180" />
      </button>
      <button
        v-for="(page, index) in visiblePageNumbers"
        :key="index"
        @click="gotToPage(page)"
        class="pagination-btn"
        :class="{
          'active-btn': isCurrentPage(page),
          'inactive-btn': !isCurrentPage(page),
        }"
      >
        <span>{{ page }}</span>
      </button>
      <button
        :disabled="isNextBtnDisabled"
        @click="gotToPage(totalPages)"
        class="pagination-btn inactive-btn"
        :class="{ 'opacity-50': isNextBtnDisabled }"
      >
        <IconsAnglesRight class="w-4 md:w-6 rotate-0" />
      </button>
      <button
        v-if="!isSmallMobile"
        @click="goToNextPage"
        :disabled="isNextBtnDisabled"
        class="pagination-btn inactive-btn"
        :class="{ 'opacity-50': isNextBtnDisabled }"
      >
        <IconsCaretDown class="w-4 md:w-6 -rotate-90" />
      </button>
    </div>
  </div>
</template>

<style scoped>
.featured {
  @apply w-full bg-[#F8F8F8] p-5 rounded-[20px];
  box-shadow: 1px 1px 4px 2px #00000034;
}
.featured-bg {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.pagination-btn {
  @apply w-12 h-12 md:w-[60px] md:h-[60px] rounded-[10px] text-xl md:text-[30px] font-bold flex items-center justify-center outline-none shadow-[0px_0px_5px_#222831];
}
.active-btn {
  @apply bg-[#EC1F27] text-white;
}
.inactive-btn {
  @apply bg-white text-[#EC1F27];
}
@media screen and (max-width: 428px) {
  .custom-height {
    min-height: 588px !important;
  }
}
</style>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAuthStore } from "~/stores/auth";
import { useCartDataStore } from "~/stores/cartData";
import { useIndexStore } from "~/stores/index";

const { apiBaseUrl, COMMON_SETTINGS } = useUrls();
const tokenCookie = useCookie("token");
const testCookie = useCookie("token");
const cartDataCookie: any = useCookie("cartData");
const { setAllCartData, setCartDataForApi } = useCartDataStore();
const { allCartData, cartDataForApi, subscriptionDataForApi } = storeToRefs(
  useCartDataStore()
);
const { setRedirectUrl } = useAuth();
const localePath = useLocalePath();
const { t } = useI18n();
const { user } = storeToRefs(useAuthStore());
const { subcriptionInfo } = storeToRefs(useIndexStore());

const nuxtApp = useNuxtApp();
const route = useRoute();
const router = useRouter();
const { isSmaller } = useBreakpoints();
const isProcessing = ref(false);
const applyCouponInput = ref(false);
const isCouponAccepted = ref(false);
const forceToLogin = ref(false);
const goToBilling = ref(false);
const showWarningModal = ref(false);
const currentBillingComp = ref("");

const isMobile = computed(() => isSmaller(768));

const cart: any = ref([]);
const selectedProductId = ref("");
const defaultSubscriptionId = ref(0);
const currentSubscriptionAmount = ref(0);
const allSubscriptions: any = ref([]);

const getSubscription = async () => {
  const { data }: any = await $fetch(
    `${apiBaseUrl}/subscriptions/video/${selectedProductId.value}`
  );

  allSubscriptions.value = data;
};

const setCartCookie = async (cart: any) => {
  cartDataCookie.value = cart;
};

const loadingCart = ref(false);
const processToCheckout = async () => {
  if (!tokenCookie.value || tokenCookie.value === "") {
    setRedirectUrl(route.fullPath + "/#checkout");
    // router.push(localePath("/auth/login"));
    await setCartCookie(cart.value);
    forceToLogin.value = true;
  } else {
    if (cart.value.couponDiscount === undefined) {
      cart.value.couponDiscount = 0;
    }

    setAllCartData(cart.value);
    // router.push(localePath("/billing"));
    currentBillingComp.value = "video";
    goToBilling.value = true;
  }
};

const loadCart = async () => {
  if (tokenCookie.value) {
    const cartDataCookie: any = useCookie("cartData");

    if (
      cartDataCookie?.value?.length > 0 &&
      cartDataCookie.value !== undefined
    ) {
      const addCartFromCookie: any = [];

      cartDataCookie.value.forEach((singleCart: any) => {
        const singleItem = {
          item_id: singleCart.id,
          subscription_id: singleCart.subscription_id,
        };
        addCartFromCookie.push(singleItem);
      });
      isProcessing.value = true;
      loadingCart.value = true;

      const { data, pending, error }: any = useFetch(
        `${apiBaseUrl}/carts/add`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
          body: {
            item_id: addCartFromCookie[0].item_id,
            subscription_id: addCartFromCookie[0].subscription_id,
          },
        }
      );

      const setData = () => {
        if (!pending.value) {
          if (data.value) {
            if (data.value.cart) {
              isProcessing.value = false;
              loadingCart.value = false;
              cart.value = [];
              cart.value.push(data.value.cart || []);

              cartDataCookie.value = JSON.stringify([]);
              const redirectUrl = useCookie("redirectUrl");

              setTimeout(() => {
                if (
                  redirectUrl.value &&
                  redirectUrl.value !== null &&
                  redirectUrl.value !== undefined &&
                  redirectUrl.value.includes("/#checkout")
                ) {
                  processToCheckout();
                  setRedirectUrl("/");
                }
              }, 300);
            } else {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: t("messages.no_item_in_cart"),
                className: "toasted-bg-success",
              });
              isProcessing.value = false;
              loadingCart.value = false;
            }
          } else if (error.value) {
            nuxtApp.$toast("clear");
            nuxtApp.$toast("error", {
              message: error.value?.data.message,
              className: "toasted-bg-alert",
            });
            isProcessing.value = false;
            loadingCart.value = false;
            cartDataCookie.value = JSON.stringify([]);
            setTimeout(() => {
              router.push(localePath("/browse-course"));
            }, 1000);
          }
        } else {
          setTimeout(() => {
            setData();
          }, 300);
        }
      };
      setData();
    } else {
      loadingCart.value = true;
      const { data, pending, error }: any = await useFetch(
        `${apiBaseUrl}/carts`,
        {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        }
      );

      const setData = () => {
        if (!pending.value) {
          if (data.value) {
            if (data.value.data.length > 0) {
              cart.value = data.value.data || [];

              const makeCardDataForApi: any = [];
              cart.value.forEach((singleCart: any) => {
                selectedProductId.value = singleCart.item.id;
                defaultSubscriptionId.value = singleCart.subscription?.id;
                currentSubscriptionAmount.value = singleCart.subscriptionTotal;
                const singleItem = {
                  item_id: singleCart.item.id,
                  subscription_id: singleCart.subscription?.id,
                };

                makeCardDataForApi.push(singleItem);
              });

              setCartDataForApi(makeCardDataForApi);
              getSubscription();
            } else {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: t("messages.no_item_in_cart"),
                className: "toasted-bg-success",
              });
            }
            isProcessing.value = false;
            loadingCart.value = false;
          } else if (error.value) {
            nuxtApp.$toast("clear");
            nuxtApp.$toast("error", {
              message: error.value?.data.message.code,
              className: "toasted-bg-success",
            });
            isProcessing.value = false;
            loadingCart.value = false;
          }
        } else {
          setTimeout(() => {
            setData();
          }, 300);
        }
      };
      setData();
    }
  } else if (!tokenCookie.value || tokenCookie.value === "") {
    const cartDataCookie: any = useCookie("cartData");
    if (cartDataCookie.value.length > 0) {
      cart.value.push(cartDataCookie.value[cartDataCookie.value.length - 1]);
      selectedProductId.value = cart.value[0].id;
      getSubscription();
    }
    isProcessing.value = false;
  }
};
loadCart();
watch(tokenCookie, () => {
  loadCart();
});

const removeProduct = async (id: number) => {
  if (tokenCookie.value) {
    const { data, error }: any = await useFetch(
      `${apiBaseUrl}/carts/${id}/delete`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${tokenCookie.value}`,
        },
      }
    );

    if (!error.value && data.value) {
      cart.value = data.value.data || [];
      loadCart();
      nuxtApp.$toast("clear");
      nuxtApp.$toast("success", {
        message: data.value.message,
        className: "toasted-bg-success",
      });
    }
  } else {
    const currentCartDataCookie: any = useCookie("cartData");

    const updatedItems = currentCartDataCookie.value.filter(
      (item: any) => item.id !== id
    );

    cartDataCookie.value = JSON.stringify(updatedItems);
    nuxtApp.$toast("clear");
    nuxtApp.$toast("success", {
      message: t("messages.delete_from_cart"),
      className: "toasted-bg-success",
    });
    cart.value = updatedItems;
  }
};

const totalPrice: any = computed(() => {
  const subTotal = ref(0);

  if (tokenCookie.value) {
    cart.value.forEach((item: any) => {
      const totalItemPrice =
        item.item?.special_price > 0
          ? item?.item?.special_price
          : item?.item?.price;
      subTotal.value =
        subTotal.value + totalItemPrice + currentSubscriptionAmount.value;
    });
  } else {
    const totalItemPrice =
      cart.value[0].special_price > 0
        ? cart.value[0].special_price
        : cart.value[0].price;

    if (
      cart.value[0]?.defaultSubcription?.id === cart.value[0]?.subscription_id
    ) {
      subTotal.value = subTotal.value + totalItemPrice;
    } else {
      subTotal.value =
        subTotal.value + totalItemPrice + currentSubscriptionAmount.value;
    }
  }
  return subTotal.value;
});

const selectSubscriptionId = ref(-1);

const updateSubscription = async (productId: any) => {
  cart.value.forEach(async (singleItemCart: any) => {
    if (singleItemCart.id === productId) {
      if (tokenCookie.value) {
        isProcessing.value = true;
        loadingCart.value = true;
        const { data, pending, error }: any = useFetch(
          `${apiBaseUrl}/carts/add`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
            body: {
              item_id: singleItemCart.item.id,
              subscription_id: singleItemCart.subscription?.id,
            },
          }
        );

        const setData = () => {
          if (!pending.value) {
            if (data.value) {
              isProcessing.value = false;
              loadCart();
            } else if (error.value) {
              isProcessing.value = false;
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message,
                className: "toasted-bg-alert",
              });
            }
            loadingCart.value = false;
          } else {
            setTimeout(() => {
              setData();
            }, 300);
          }
        };
        setData();
      } else {
        cart.value.forEach((singleCart: any) => {
          if (singleCart.id === productId) {
            singleCart.subscription_id = singleItemCart.subscription_id;
          }
        });
      }
    }
  });
};

const getSubscriptionDuration = (productId: number) => {
  const isExist = allSubscriptions.value.find(
    (singleCartData: any) => singleCartData.id === productId
  );
  if (isExist) {
    return isExist?.durations_as_month;
  }
};

const getSubscriptionAmount = (productId: number) => {
  const isExist = allSubscriptions.value.find(
    (singleCartData: any) => singleCartData.id === productId
  );
  if (isExist) {
    let defaultSubcriptionDays = 0;
    if (cartDataCookie.value[0]?.defaultSubcription) {
      defaultSubcriptionDays =
        cartDataCookie.value[0]?.defaultSubcription?.durations_as_days;
    } else {
      defaultSubcriptionDays =
        cartDataCookie.value[0]?.defaultsubcription?.durations_as_days;
    }
    const selectedSubcriptionDays = isExist.durations_as_days;
    const subscriptionPerMonth = cartDataCookie.value[0].monthly_service_charge;
    const subscriptionPerDay = subscriptionPerMonth / 30;
    const realDays = selectedSubcriptionDays - defaultSubcriptionDays;
    const totalAmount = realDays * subscriptionPerDay;
    currentSubscriptionAmount.value = totalAmount;
    return totalAmount;
  }
};

const applyCoupon = ref("");
const couponDiscount: any = ref("0");
const handleApplyCoupon = () => {
  if (applyCoupon.value !== "") {
    isProcessing.value = true;
    const selectedCart: any = [];

    cart.value.forEach((singleCart: any) => {
      const singleItem = {
        item_id:
          tokenCookie.value !== "" && tokenCookie.value !== undefined
            ? singleCart.item.id
            : singleCart.id,
        subscription_id: singleCart.subscription_id
          ? singleCart.subscription_id
          : singleCart.subscription?.id,
      };
      selectedCart.push(singleItem);
    });

    const { data, pending, error }: any = useFetch(
      `${apiBaseUrl}/carts/apply-coupon`,
      {
        method: "POST",
        body: {
          code: applyCoupon.value,
          items: selectedCart,
        },
      }
    );

    const setData = () => {
      if (!pending.value) {
        if (data.value) {
          couponDiscount.value = data._rawValue.data;
          cart.value.couponDiscount = couponDiscount.value;
          cart.value.code = applyCoupon.value;
          isProcessing.value = false;
          isCouponAccepted.value = true;
          nuxtApp.$toast("clear");
          nuxtApp.$toast("success", {
            message: t("messages.coupon_applied"),
            className: "toasted-bg-success",
          });
        } else if (error.value) {
          nuxtApp.$toast("clear");
          if (
            error.value.statusCode === 422 ||
            error.value.statusCode === 404
          ) {
            nuxtApp.$toast("error", {
              message: error.value.data.message,
              className: "toasted-bg-success",
            });
          } else if (error.value.statusCode === 403) {
            nuxtApp.$toast("error", {
              message: error.value.data.data,
              className: "toasted-bg-success",
            });
          } else {
            nuxtApp.$toast("error", {
              message: error.value.data.message,
              className: "toasted-bg-success",
            });
          }
          isProcessing.value = false;
        }
      } else {
        setTimeout(() => {
          setData();
        }, 300);
      }
    };

    setData();
  }
};

const afterAppliedCoupon = (totalPriceValue: any, couponDiscountValue: any) => {
  const grandTotal =
    totalPriceValue - parseFloat(couponDiscountValue.replace(/,/g, ""));

  cart.value.grandTotal = totalPrice.value;
  cart.value.code = applyCoupon.value;
  cart.value.couponDiscount = couponDiscount.value;
  cart.value.subscriptionPrice = currentSubscriptionAmount.value;
  return grandTotal;
};

const isTokenCookieEmpty = computed(() => {
  return !tokenCookie.value || tokenCookie.value === "";
});

const setForceToLogin = () => {
  setTimeout(() => {
    forceToLogin.value = false;
  }, 500);
};

const backTo = () => {
  if (goToBilling.value) {
    goToBilling.value = false;
  } else {
    router.back();
  }
};
</script>

<template>
  <div class="p-5 md:py-[50px]">
    <div class="custom-container flex flex-col space-y-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center justify-start space-x-3" @click="backTo">
          <IconsLeftArrow class="w-5 h-5 md:w-6 md:h-6 cursor-pointer" />
          <span class="text-[18px] font-semibold line-clamp-1">
            {{ $t("my_cart") }}
          </span>
        </div>
      </div>
      <WarningModal
        v-if="showWarningModal"
        :showWarningModal="showWarningModal"
        @closeAppModal="showWarningModal = false"
      />
    </div>
    <div class="w-full md:w-3/5 mx-auto mt-8">
      <div class="px-12">
        <ol class="flex items-center">
          <li
            :class="tokenCookie ? 'w-full' : 'w-3/5'"
            class="flex relative items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
          >
            <span
              :class="
                !goToBilling && !forceToLogin
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 '
              "
              class="flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0"
              >1</span
            >
            <p
              class="absolute top-14 left-[-45px] font-semibold text-black text-base md:text-base md:text-[20px]"
            >
              {{ $t("order_confirmation") }}
            </p>
          </li>
          <li
            v-if="!tokenCookie"
            class="flex relative w-3/5 items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-gray-100 after:border-2 after:inline-block"
          >
            <span
              :class="forceToLogin ? 'bg-red-600 text-white' : 'bg-gray-200 '"
              class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
              >2</span
            >
            <p
              class="absolute top-14 left-[-5px] font-semibold text-black text-base md:text-[20px]"
            >
              {{ $t("login") }}
            </p>
          </li>
          <li v-if="!tokenCookie" class="flex relative items-center">
            <span
              class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
              >3</span
            >
            <p
              class="absolute top-14 left-[-10px] font-semibold text-black text-base md:text-[20px]"
            >
              {{ $t("payment") }}
            </p>
          </li>
          <li v-if="tokenCookie" class="flex relative items-center w-20">
            <span
              :class="!goToBilling ? 'bg-gray-200' : 'bg-red-600 text-white'"
              class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
              >2</span
            >
            <p
              class="absolute top-14 left-[-10px] font-semibold text-black text-base md:text-[20px]"
            >
              {{ $t("payment") }}
            </p>
          </li>
        </ol>
      </div>
    </div>

    <!-- Order Confirmation Section -->
    <div
      v-if="!goToBilling"
      class="custom-container flex flex-col lg:flex-row justify-between gap-4 md:gap-10 xl:gap-16"
    >
      <!-- Course Details -->
      <div
        v-for="product in cart"
        class="flex flex-col gap-5 w-full lg:w-1/2 mt-24"
      >
        <div class="flex gap-5 border-2 w-full py-8 px-5 h-[140px]">
          <img
            v-if="
              (product.item?.banner_url || product.banner_url) &&
              product.item?.banner_url !== 'null' &&
              product.banner_url !== 'null'
            "
            class="w-[130px] h-[75px] object-cover"
            :src="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.banner_url
                : product.banner_url
            "
            :alt="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.title
                : product.title
            "
          />
          <img
            v-else
            class="w-[80px] object-cover aspect-[10/9]"
            src="/images/course-details/courseIcon.png"
            :alt="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.title
                : product.title
            "
          />
          <div class="">
            <p
              class="text-lg md:text-[24px] font-semibold text-primary-red line-clamp-2"
            >
              {{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product.item?.title
                  : product.title
              }}
            </p>
            <p
              v-if="
                product?.item?.special_price > 0 || product?.special_price > 0
              "
              class="mr-2 text-lg md:text-[24px] text-gray-600"
            >
              ৳{{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product?.item?.special_price
                  : product?.special_price
              }}
            </p>
          </div>
        </div>
        <div
          v-if="subcriptionInfo && subcriptionInfo[1]"
          class="subscription border-2 w-full py-8 px-5 word-break"
          v-html="subcriptionInfo[1]"
        ></div>
      </div>

      <!-- Course Price -->
      <div v-if="!forceToLogin" class="w-full lg:w-1/2">
        <div v-for="product in cart" class="border-2 py-8 px-5 mt-24">
          <div class="flex justify-between">
            <p class="text-xl md:text-2xl font-semibold">
              {{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product?.defaultsubscription?.name
                  : product?.defaultSubcription?.name
              }}
            </p>
            <div class="flex text-xl md:text-2xl font-semibold">
              <p
                v-if="
                  product?.item?.special_price > 0 || product?.special_price > 0
                "
                class="mr-2"
              >
                ৳{{
                  tokenCookie !== "" && tokenCookie !== undefined
                    ? product?.item?.special_price
                    : product?.special_price
                }}
              </p>
              <p
                :class="
                  product?.item?.special_price > 0 || product?.special_price > 0
                    ? 'line-through'
                    : ``
                "
              >
                <span
                  v-if="
                    product?.item?.special_price === 0 ||
                    product?.special_price === 0
                  "
                  >৳</span
                >{{
                  tokenCookie !== "" && tokenCookie !== undefined
                    ? product.item?.price
                    : product?.price
                }}
              </p>
            </div>
          </div>
          <div
            v-if="!applyCouponInput"
            @click="applyCouponInput = !applyCouponInput"
            class="flex justify-between items-center cursor-pointer my-6"
          >
            <p class="font-semibold text-primary-red text-[24px]">
              {{ $t("apply_coupon_here") }}
            </p>
            <div
              class="py-1.5 px-4 rounded-lg border border-gray-300 flex justify-center items-center text-base font-medium text-center"
            >
              <span>{{ $t("click_here_btn") }}</span>
            </div>
          </div>
          <div v-if="applyCouponInput && !isCouponAccepted">
            <div>
              <input
                v-model="applyCoupon"
                :placeholder="$t('apply_coupon_here')"
                class="border-2 mt-4 px-4 border-black rounded-[10px] w-full h-[50px]"
                type="text"
              />
            </div>
            <div class="flex justify-between pt-[10px] pb-4">
              <button
                @click="applyCouponInput = !applyCouponInput"
                class="border-2 text-base md:text-[20px] border-black h-[50px] w-[210px]"
              >
                {{ $t("cancel") }}
              </button>
              <button
                @click="handleApplyCoupon"
                :disabled="isProcessing"
                class="bg-black text-white h-[50px] w-[210px] text-base md:text-[20px]"
              >
                {{ $t("submit") }}
              </button>
            </div>
          </div>
          <div v-if="isCouponAccepted" class="flex mt-5 justify-between">
            <p class="text-base md:text-[20px]">
              {{ $t("promo_code") }}: <span class="">{{ applyCoupon }}</span>
            </p>
            <p class="text-base md:text-[20px] text-primary-red">
              - ৳{{ $bdNumberFormat(couponDiscount) }}
            </p>
          </div>

          <div class="flex mt-5 justify-between items-center">
            <div class="flex items-center">
              <p class="text-base md:text-[20px]">
                {{ $t("course_duration") }}
              </p>

              <div v-if="tokenCookie !== '' && tokenCookie !== undefined">
                <div class="relative">
                  <select
                    v-if="product.subscription?.id"
                    class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                    id="selectedSubscription"
                    name="SelectedSubscription"
                    v-model="product.subscription.id"
                    @change="updateSubscription(product.id)"
                  >
                    <option selected :value="product?.defaultsubscription?.id">
                      {{ $t("select") }}
                    </option>
                    <option
                      v-for="(option, index) in allSubscriptions"
                      :key="index"
                      :value="option.id"
                    >
                      {{ option?.durations_as_days }}
                      {{
                        option?.durations_as_days > 1 ? $t("days") : $t("day")
                      }}
                    </option>
                  </select>

                  <IconsCaretDown
                    class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                  />
                </div>
              </div>
              <div v-else>
                <div class="relative">
                  <select
                    class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                    id="selectedSubscription"
                    name="SelectedSubscription"
                    v-model="product.subscription_id"
                    @change="updateSubscription(product.id)"
                  >
                    <option selected :value="product?.defaultSubcription.id">
                      {{ $t("select") }}
                    </option>
                    <option
                      v-for="(option, index) in allSubscriptions"
                      :key="index"
                      :value="option.id"
                    >
                      {{ option?.durations_as_days }}
                      {{
                        option?.durations_as_days > 1 ? $t("days") : $t("day")
                      }}
                    </option>
                  </select>

                  <IconsCaretDown
                    class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                  />
                </div>
              </div>
            </div>
            <template
              v-if="
                product?.defaultsubcription?.id !== product?.subscription?.id
              "
            >
              <div
                v-if="product?.subscriptionTotal"
                class="text-base md:text-[20px]"
              >
                ৳{{ product?.subscriptionTotal.toFixed(2) }}
              </div>
            </template>
            <template
              v-if="
                product?.defaultSubcription?.id !== product?.subscription_id
              "
            >
              <div class="text-base md:text-[20px]">
                ৳{{
                  getSubscriptionAmount(product.subscription_id)?.toFixed(2)
                }}
              </div>
            </template>
          </div>
          <div v-if="false" class="flex justify-between items-center mt-5">
            <p class="text-base md:text-[20px]">{{ $t("monthly_cost") }}</p>
            <p v-if="tokenCookie !== '' && tokenCookie !== undefined">
              {{ product?.subscription?.durations_as_month }} x ৳{{
                product?.item?.monthly_service_charge
              }}
            </p>
            <p v-else>
              {{ getSubscriptionDuration(product?.subscription_id) }} x ৳{{
                product.monthly_service_charge
              }}
            </p>
          </div>
          <div
            class="flex justify-between flex-row w-full py-2 border-t border-black my-[20px]"
          >
            <p class="font-semibold text-base md:text-[20px]">
              {{ $t("total") }}
            </p>
            <p class="font-semibold text-base md:text-[20px]">
              ৳{{ afterAppliedCoupon(totalPrice, couponDiscount).toFixed(2) }}
            </p>
          </div>
          <div>
            <button
              @click="processToCheckout"
              :disabled="loadingCart"
              class="bg-black text-white h-[50px] w-full text-base md:text-[20px]"
              :class="loadingCart ? 'opacity-50' : ''"
            >
              {{ $t("get_started") }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="forceToLogin" class="w-full lg:w-1/2 py-8 md:px-5 mt-16">
        <AuthLogin
          @loggedInSuccess="setForceToLogin"
          @signupSuccess="setForceToLogin"
          class="flex flex-col items-center justify-center"
        />
      </div>
    </div>
    <div v-else>
      <Billing :currentBillingComp="currentBillingComp" />
    </div>
    <div v-if="cart && cart.length > 0" class="pb-10 w-full h-full hidden">
      <div>
        <h3
          class="text-2xl md:text-[28px] lg:text-[30px] font-semibold text-primary-red"
        >
          {{ $t("cart") }}
        </h3>
        <div class="overflow-x-auto pt-4 lg:pt-10">
          <table
            class="w-full table-auto border-collapse border border-black whitespace-nowrap text-black"
          >
            <thead class="font-semibold">
              <tr>
                <th>{{ $t("course") }}</th>
                <th>{{ $t("subscription") }}</th>
                <th>{{ $t("price") }}</th>
                <th>{{ $t("sub_total") }}</th>
                <th>{{ $t("total") }}</th>
                <th>{{ $t("action") }}</th>
              </tr>
            </thead>

            <tbody>
              <tr
                v-for="product in cart"
                :key="product.id"
                :class="product.id % 2 === 0 ? 'bg-[#F5F5F5]' : 'bg-white'"
              >
                <td>
                  <div class="flex items-center gap-2 min-w-[130px]">
                    <img
                      v-if="
                        (product.item?.banner_url || product.banner_url) &&
                        product.item?.banner_url !== 'null' &&
                        product.banner_url !== 'null'
                      "
                      class="w-8 h-8 object-cover"
                      :src="
                        tokenCookie !== '' && tokenCookie !== undefined
                          ? product.item?.banner_url
                          : product.banner_url
                      "
                      :alt="
                        tokenCookie !== '' && tokenCookie !== undefined
                          ? product.item?.title
                          : product.title
                      "
                    />
                    <img
                      v-else
                      class="w-8 h-8 object-cover"
                      src="/images/course-details/courseIcon.png"
                      :alt="
                        tokenCookie !== '' && tokenCookie !== undefined
                          ? product.item?.title
                          : product.title
                      "
                    />
                    <p class="truncate">
                      {{
                        tokenCookie !== "" && tokenCookie !== undefined
                          ? product.item?.title
                          : product.title
                      }}
                    </p>
                  </div>
                </td>

                <!-- Start Subscription column -->
                <td v-if="tokenCookie !== '' && tokenCookie !== undefined">
                  <div class="relative" @click="updateSubscription(product.id)">
                    <select
                      v-if="product.subscription?.id"
                      class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                      id="selectedSubscription"
                      name="SelectedSubscription"
                      v-model="product.subscription.id"
                    >
                      <option
                        v-for="(option, index) in allSubscriptions"
                        :key="index"
                        :value="option.id"
                      >
                        {{ option?.durations_as_month }}
                        {{
                          option?.durations_as_month > 1
                            ? $t("months")
                            : $t("month")
                        }}
                      </option>
                    </select>

                    <IconsCaretDown
                      class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                    />
                  </div>
                </td>
                <td v-else>
                  <div class="relative">
                    <select
                      class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                      id="selectedSubscription"
                      name="SelectedSubscription"
                      v-model="product.subscription_id"
                    >
                      <option
                        v-for="(option, index) in allSubscriptions"
                        :key="index"
                        :value="option.id"
                      >
                        {{ option?.durations_as_month }}
                        {{
                          option?.durations_as_month > 1
                            ? $t("months")
                            : $t("month")
                        }}
                      </option>
                    </select>

                    <IconsCaretDown
                      class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                    />
                  </div>
                </td>
                <!-- End Subscription column -->

                <td>
                  <div class="flex">
                    <p
                      v-if="
                        product?.item?.special_price > 0 ||
                        product?.special_price > 0
                      "
                      class="mr-2"
                    >
                      ৳{{
                        tokenCookie !== "" && tokenCookie !== undefined
                          ? product?.item?.special_price
                          : product?.special_price
                      }}
                    </p>
                    <p
                      :class="
                        product?.item?.special_price > 0 ||
                        product?.special_price > 0
                          ? 'line-through'
                          : ``
                      "
                    >
                      <span
                        v-if="
                          product?.item?.special_price === 0 ||
                          product?.special_price === 0
                        "
                        >৳</span
                      >{{
                        tokenCookie !== "" && tokenCookie !== undefined
                          ? product.item?.price
                          : product?.price
                      }}
                    </p>
                  </div>
                </td>

                <td
                  class="has-tooltip"
                  v-if="tokenCookie !== '' && tokenCookie !== undefined"
                >
                  {{ product?.subscription?.durations_as_month }} x ৳{{
                    product?.item?.monthly_service_charge
                  }}
                  + ৳{{ product?.subscription?.amount }} + ৳{{
                    product.item?.special_price > 0
                      ? product.item?.special_price
                      : product.item?.price
                  }}

                  <span class="tooltip">
                    {{ $t("duration_month") }}
                    {{ product.subscription?.durations_as_month }}
                    <br />
                    {{ $t("monthly_charge") }} ৳{{
                      product.item?.monthly_service_charge
                    }}
                    <br />
                    {{ $t("subscription_const") }} ৳{{
                      product.subscription?.amount
                    }}
                    <br />
                    {{ $t("course_price") }} ৳{{
                      product.item?.special_price > 0
                        ? product.item?.special_price
                        : product.item?.price
                    }}</span
                  >
                </td>

                <td v-else class="has-tooltip">
                  {{ getSubscriptionDuration(product?.subscription_id) }} x ৳{{
                    product.monthly_service_charge
                  }}
                  + ৳{{ getSubscriptionAmount(product?.subscription_id) }} + ৳{{
                    product.special_price > 0
                      ? product?.special_price
                      : product?.price
                  }}
                  <span class="tooltip">
                    {{ $t("duration_month") }}
                    {{ getSubscriptionDuration(product?.subscription_id) }}
                    <br />
                    {{ $t("monthly_charge") }} ৳{{
                      product?.monthly_service_charge
                    }}
                    <br />
                    {{ $t("subscription_const") }} ৳{{
                      getSubscriptionAmount(product?.subscription_id)
                    }}
                    <br />
                    {{ $t("course_price") }} ৳{{
                      product?.special_price > 0
                        ? product?.special_price
                        : product?.price
                    }}
                  </span>
                </td>

                <!-- <td>
                  ৳{{
                    tokenCookie !== "" && tokenCookie !== undefined
                      ? productPrice(
                          product?.id,
                          product.subscription?.durations_as_month,
                          product.item?.monthly_service_charge,
                          product.subscription?.amount,
                          product.item?.special_price,
                          product.item?.price
                        )
                      : productPrice(
                          product.id,
                          getSubscriptionDuration(product?.subscription_id),
                          product.monthly_service_charge,
                          getSubscriptionAmount(product?.subscription_id),
                          product.special_price,
                          product.price
                        )
                  }}
                </td>

                <td @click="removeProduct(product.id)">
                  <IconsCross
                    class="text-primary-red w-[18px] h-[18px] cursor-pointer mx-auto"
                  />
                </td> -->
              </tr>

              <tr v-if="!isMobile">
                <td colspan="6">
                  <div
                    class="whitespace-nowrap text-black flex justify-end gap-5"
                  >
                    <div class="flex items-center gap-5">
                      <input
                        v-model="applyCoupon"
                        class="text-center w-40 h-10 outline-none border-2 border-[#ec1f27] rounded"
                        type="text"
                        :placeholder="$t('coupon_code')"
                      />
                      <button
                        class="text-center w-40 h-10 outline-none border border-[#EC1F271E] text-primary-red font-semibold rounded"
                        :disabled="isProcessing"
                        @click="handleApplyCoupon"
                      >
                        {{ $t("apply_coupon_btn") }}
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div
          v-if="isMobile"
          class="text-sm pt-4 whitespace-nowrap text-black flex justify-between gap-4"
        >
          <input
            v-model="applyCoupon"
            class="text-center w-1/3 h-10 outline-none border border-[#EC1F271E] rounded"
            type="text"
            :placeholder="$t('coupon_code')"
          />
          <button
            class="text-center w-1/3 h-10 outline-none border border-[#EC1F271E] text-primary-red font-semibold rounded"
            @click="handleApplyCoupon"
            :disabled="isProcessing"
          >
            {{ $t("apply_coupon_btn") }}
          </button>
          <button
            class="text-center w-1/3 h-10 outline-none border border-[#EC1F271E] text-primary-red font-semibold rounded"
          >
            {{ $t("update_cart_btn") }}
          </button>
        </div>
      </div>

      <div class="flex justify-between pt-16 md:pt-20">
        <div class="hidden md:block"></div>
        <div class="w-full md:w-1/2">
          <h3
            class="w-full text-2xl md:text-[28px] lg:text-[30px] font-semibold text-primary-red"
          >
            {{ $t("cart_totals") }}
          </h3>
          <div
            class="mt-5 w-full text-base md:text-lg border border-black rounded-xl"
          >
            <div class="flex flex-row w-full py-2 px-5 border-b border-black">
              <div class="w-1/3 font-semibold">{{ $t("sub_total") }}</div>
              <div class="w-2/3">৳{{ $bdNumberFormat(totalPrice) }}</div>
            </div>
            <div class="flex flex-row w-full py-2 px-5 border-b border-black">
              <div class="w-1/3 font-semibold">{{ $t("coupon") }}</div>
              <div class="w-2/3">
                <span class="font-bold text-2xl">-</span> ৳{{
                  $bdNumberFormat(couponDiscount)
                }}
              </div>
            </div>
            <div class="flex flex-row w-full py-2 px-5">
              <div class="w-1/3 font-semibold">{{ $t("total") }}</div>
              <div class="w-2/3">
                ৳{{
                  $bdNumberFormat(
                    afterAppliedCoupon(totalPrice, couponDiscount)
                  )
                }}
              </div>
            </div>
          </div>
          <button
            class="mt-5 lg:mt-[52px] text-base xl:text-lg font-bold w-full h-10 xl:h-[50px] outline-none text-white bg-primary-red rounded-xl"
            @click="processToCheckout"
          >
            {{ $t("checkout_btn") }}
          </button>
        </div>
      </div>
    </div>
    <div
      class="w-full h-full flex flex-col justify-center items-center pt-[140px]"
      v-else
    >
      <IconsNoData />
      <p class="text-lg pt-5">{{ $t("no_data_text") }}</p>
    </div>
  </div>
</template>

<style scoped>
th,
td {
  @apply text-left min-w-[80px] text-base lg:text-lg border border-black py-2 lg:py-3 px-5;
}
.has-tooltip {
  @apply relative;
}
.tooltip {
  @apply absolute bg-primary-red text-white z-[100] left-20 -top-[56px] lg:-top-24 text-left invisible p-1.5 px-4 rounded-xl  shadow-lg w-full min-w-[300px] whitespace-normal break-words;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.word-break {
  word-wrap: break-word;
}
</style>

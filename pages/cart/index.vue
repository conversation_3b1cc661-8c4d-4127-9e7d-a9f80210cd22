<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useCartDataStore } from "~/stores/cartData";
import { useIndexStore } from "~/stores/index";

const { apiBaseUrl, BANK_ACCOUNT } = useUrls();
const tokenCookie = useCookie("token");
const cartDataCookie: any = useCookie("cartData");
const { setRedirectUrl } = useAuth();
const localePath = useLocalePath();
const { t } = useI18n();
const { subcriptionInfo } = storeToRefs(useIndexStore());
const { setAllCartData, setCartDataForApi } = useCartDataStore();

const nuxtApp = useNuxtApp();
const route = useRoute();
const router = useRouter();
const isProcessing = ref(false);
const applyCouponInput = ref(false);
const isCouponAccepted = ref(false);
const forceToLogin = ref(false);
const goToBilling = ref(false);
const showWarningModal = ref(false);
const currentBillingComp = ref("");
const cart: any = ref([]);
const selectedProductId = ref("");
const defaultSubscriptionId = ref(0);
const currentSubscriptionAmount = ref(0);
const allSubscriptions: any = ref([]);
const paymentStep = ref("");
const applyCoupon = ref("");
const couponDiscount: any = ref("0");
const loadingCart = ref(false);

const localTokenFromIOS = computed(() =>
  route.query?.iosToken ? (route.query.iosToken as string) : null
);

const getSubscription = async () => {
  const { data }: any = await $fetch(
    `${apiBaseUrl}/subscriptions/video/${selectedProductId.value}`
  );

  allSubscriptions.value = data;
};

const setCartCookie = async (cart: any) => {
  cartDataCookie.value = cart;
};

const processToCheckout = async () => {
  if (!tokenCookie.value || tokenCookie.value === "") {
    setRedirectUrl(route.fullPath + "/#checkout");
    // router.push(localePath("/auth/login"));
    await setCartCookie(cart.value);
    forceToLogin.value = true;
  } else {
    if (cart.value.couponDiscount === undefined) {
      cart.value.couponDiscount = 0;
    }

    setAllCartData(cart.value);
    // router.push(localePath("/billing"));
    currentBillingComp.value = "video";
    goToBilling.value = true;
  }
  paymentStep.value = "billing";
  window.scrollTo(0, 0);
};

const loadCart = async () => {
  if (tokenCookie.value) {
    const cartDataCookie: any = useCookie("cartData");

    if (
      cartDataCookie?.value?.length > 0 &&
      cartDataCookie.value !== undefined
    ) {
      const addCartFromCookie: any = [];

      cartDataCookie.value.forEach((singleCart: any) => {
        const singleItem = {
          item_id: singleCart.id,
          subscription_id: singleCart.subscription_id,
        };
        addCartFromCookie.push(singleItem);
      });
      isProcessing.value = true;
      loadingCart.value = true;

      const { data, pending, error }: any = useFetch(
        `${apiBaseUrl}/carts/add`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
          body: {
            item_id: addCartFromCookie[0].item_id,
            subscription_id: addCartFromCookie[0].subscription_id,
          },
        }
      );

      const setData = () => {
        if (!pending.value) {
          if (data.value) {
            if (data.value.cart) {
              isProcessing.value = false;
              loadingCart.value = false;
              cart.value = [];
              cart.value.push(data.value.cart || []);

              cartDataCookie.value = JSON.stringify([]);
              const redirectUrl = useCookie("redirectUrl");

              setTimeout(() => {
                if (
                  redirectUrl.value &&
                  redirectUrl.value !== null &&
                  redirectUrl.value !== undefined &&
                  redirectUrl.value.includes("/#checkout")
                ) {
                  processToCheckout();
                  setRedirectUrl("/");
                }
              }, 300);
            } else {
              if (import.meta.client) {
                nuxtApp.$toast("clear");
                nuxtApp.$toast("error", {
                  message: t("messages.no_item_in_cart"),
                  className: "toasted-bg-success",
                });
              }
              isProcessing.value = false;
              loadingCart.value = false;
            }
          } else if (error.value) {
            if (import.meta.client) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message,
                className: "toasted-bg-alert",
              });
            }
            isProcessing.value = false;
            loadingCart.value = false;
            cartDataCookie.value = JSON.stringify([]);
            setTimeout(() => {
              router.push(localePath("/browse-course"));
            }, 1000);
          }
        } else {
          setTimeout(() => {
            setData();
          }, 300);
        }
      };
      setData();
    } else {
      loadingCart.value = true;
      const { data, pending, error }: any = await useFetch(
        `${apiBaseUrl}/carts`,
        {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        }
      );

      const setData = () => {
        if (!pending.value) {
          if (data.value) {
            if (data.value.data.length > 0) {
              cart.value = data.value.data || [];

              const makeCardDataForApi: any = [];
              cart.value.forEach((singleCart: any) => {
                selectedProductId.value = singleCart.item.id;
                defaultSubscriptionId.value = singleCart.subscription?.id;
                currentSubscriptionAmount.value = singleCart.subscriptionTotal;
                const singleItem = {
                  item_id: singleCart.item.id,
                  subscription_id: singleCart.subscription?.id,
                };

                makeCardDataForApi.push(singleItem);
              });

              setCartDataForApi(makeCardDataForApi);
              getSubscription();
            } else {
              if (import.meta.client) {
                nuxtApp.$toast("clear");
                nuxtApp.$toast("error", {
                  message: t("messages.no_item_in_cart"),
                  className: "toasted-bg-success",
                });
              }
            }
            isProcessing.value = false;
            loadingCart.value = false;
          } else if (error.value) {
            if (import.meta.client) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message.code,
                className: "toasted-bg-success",
              });
            }
            isProcessing.value = false;
            loadingCart.value = false;
          }
        } else {
          setTimeout(() => {
            setData();
          }, 300);
        }
      };
      setData();
    }
  } else if (!tokenCookie.value || tokenCookie.value === "") {
    const cartDataCookie: any = useCookie("cartData");
    if (cartDataCookie.value.length > 0) {
      cart.value.push(cartDataCookie.value[cartDataCookie.value.length - 1]);
      selectedProductId.value = cart.value[0].id;
      getSubscription();
    }
    isProcessing.value = false;
  }
};
loadCart();

watch(tokenCookie, () => {
  loadCart();
});

const removeProduct = async (id: number) => {
  if (tokenCookie.value) {
    const { data, error }: any = await useFetch(
      `${apiBaseUrl}/carts/${id}/delete`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${tokenCookie.value}`,
        },
      }
    );

    if (!error.value && data.value) {
      cart.value = data.value.data || [];
      loadCart();
      nuxtApp.$toast("clear");
      nuxtApp.$toast("success", {
        message: data.value.message,
        className: "toasted-bg-success",
      });
    }
  } else {
    const currentCartDataCookie: any = useCookie("cartData");

    const updatedItems = currentCartDataCookie.value.filter(
      (item: any) => item.id !== id
    );

    cartDataCookie.value = JSON.stringify(updatedItems);
    nuxtApp.$toast("clear");
    nuxtApp.$toast("success", {
      message: t("messages.delete_from_cart"),
      className: "toasted-bg-success",
    });
    cart.value = updatedItems;
  }
};

const totalPrice: any = computed(() => {
  const subTotal = ref(0);

  if (tokenCookie.value) {
    cart.value.forEach((item: any) => {
      const totalItemPrice =
        item.item?.special_price > 0
          ? item?.item?.special_price
          : item?.item?.price;
      subTotal.value =
        subTotal.value + totalItemPrice + currentSubscriptionAmount.value;
    });
  } else {
    const totalItemPrice =
      cart.value[0].special_price > 0
        ? cart.value[0].special_price
        : cart.value[0].price;

    if (
      cart.value[0]?.defaultSubcription?.id === cart.value[0]?.subscription_id
    ) {
      subTotal.value = subTotal.value + totalItemPrice;
    } else {
      subTotal.value =
        subTotal.value + totalItemPrice + currentSubscriptionAmount.value;
    }
  }
  return subTotal.value;
});

const updateSubscription = async (productId: any) => {
  cart.value.forEach(async (singleItemCart: any) => {
    if (singleItemCart.id === productId) {
      if (tokenCookie.value) {
        isProcessing.value = true;
        loadingCart.value = true;
        const { data, pending, error }: any = useFetch(
          `${apiBaseUrl}/carts/add`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
            body: {
              item_id: singleItemCart.item.id,
              subscription_id: singleItemCart.subscription?.id,
            },
          }
        );

        const setData = () => {
          if (!pending.value) {
            if (data.value) {
              isProcessing.value = false;
              loadCart();
            } else if (error.value) {
              isProcessing.value = false;
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message,
                className: "toasted-bg-alert",
              });
            }
            loadingCart.value = false;
          } else {
            setTimeout(() => {
              setData();
            }, 300);
          }
        };
        setData();
      } else {
        cart.value.forEach((singleCart: any) => {
          if (singleCart.id === productId) {
            singleCart.subscription_id = singleItemCart.subscription_id;
          }
        });
      }
    }
  });
};

const getSubscriptionDuration = (productId: number) => {
  const isExist = allSubscriptions.value.find(
    (singleCartData: any) => singleCartData.id === productId
  );
  if (isExist) {
    return isExist?.durations_as_month;
  }
};

const getSubscriptionAmount = (productId: number) => {
  const isExist = allSubscriptions.value.find(
    (singleCartData: any) => singleCartData.id === productId
  );
  if (isExist) {
    let defaultSubcriptionDays = 0;
    if (cartDataCookie.value[0]?.defaultSubcription) {
      defaultSubcriptionDays =
        cartDataCookie.value[0]?.defaultSubcription?.durations_as_days;
    } else {
      defaultSubcriptionDays =
        cartDataCookie.value[0]?.defaultsubcription?.durations_as_days;
    }
    const selectedSubcriptionDays = isExist.durations_as_days;
    const subscriptionPerMonth = cartDataCookie.value[0].monthly_service_charge;
    const subscriptionPerDay = subscriptionPerMonth / 30;
    const realDays = selectedSubcriptionDays - defaultSubcriptionDays;
    const totalAmount = realDays * subscriptionPerDay;
    currentSubscriptionAmount.value = totalAmount;
    return totalAmount;
  }
};

const handleApplyCoupon = () => {
  if (applyCoupon.value !== "") {
    isProcessing.value = true;
    const selectedCart: any = [];

    cart.value.forEach((singleCart: any) => {
      const singleItem = {
        item_id:
          tokenCookie.value !== "" && tokenCookie.value !== undefined
            ? singleCart.item.id
            : singleCart.id,
        subscription_id: singleCart.subscription_id
          ? singleCart.subscription_id
          : singleCart.subscription?.id,
      };
      selectedCart.push(singleItem);
    });

    const { data, pending, error }: any = useFetch(
      `${apiBaseUrl}/carts/apply-coupon`,
      {
        method: "POST",
        body: {
          code: applyCoupon.value,
          items: selectedCart,
        },
      }
    );

    const setData = () => {
      if (!pending.value) {
        if (data.value) {
          couponDiscount.value = data._rawValue.data;
          cart.value.couponDiscount = couponDiscount.value;
          cart.value.code = applyCoupon.value;
          isProcessing.value = false;
          isCouponAccepted.value = true;
          nuxtApp.$toast("clear");
          nuxtApp.$toast("success", {
            message: t("messages.coupon_applied"),
            className: "toasted-bg-success",
          });
        } else if (error.value) {
          nuxtApp.$toast("clear");
          if (
            error.value.statusCode === 422 ||
            error.value.statusCode === 404
          ) {
            nuxtApp.$toast("error", {
              message: error.value.data.message,
              className: "toasted-bg-success",
            });
          } else if (error.value.statusCode === 403) {
            nuxtApp.$toast("error", {
              message: error.value.data.data,
              className: "toasted-bg-success",
            });
          } else {
            nuxtApp.$toast("error", {
              message: error.value.data.message,
              className: "toasted-bg-success",
            });
          }
          isProcessing.value = false;
        }
      } else {
        setTimeout(() => {
          setData();
        }, 300);
      }
    };

    setData();
  }
};

const afterAppliedCoupon = (totalPriceValue: any, couponDiscountValue: any) => {
  const grandTotal =
    totalPriceValue - parseFloat(couponDiscountValue.replace(/,/g, ""));

  cart.value.grandTotal = totalPrice.value;
  cart.value.code = applyCoupon.value;
  cart.value.couponDiscount = couponDiscount.value;
  cart.value.subscriptionPrice = currentSubscriptionAmount.value;
  return grandTotal.toFixed(2);
};

const setForceToLogin = () => {
  setTimeout(() => {
    forceToLogin.value = false;
  }, 500);
};

const { data: banksData } = await useFetch<any>(`${BANK_ACCOUNT}?per_page=-1`);

const banks = computed(() => {
  const mobileBanking = banksData.value.data?.filter(
    (bank: any) => bank?.type === "mobile banking"
  );
  const onlineBanking = banksData.value.data?.filter(
    (bank: any) => bank?.type === "online banking"
  );
  return { mobileBanking, onlineBanking };
});

const backTo = () => {
  if (paymentStep.value === "status") {
    router.back();
  } else if (paymentStep.value === "manualPayment") {
    paymentStep.value = "billing";
  } else if (goToBilling.value) {
    goToBilling.value = false;
    paymentStep.value = "";
  } else {
    router.back();
  }
};

const submitPayment = () => {
  paymentStep.value = "status";
  window.scrollTo(0, 0);
  setAllCartData([]);
  cart.value = [];
};
</script>

<template>
  <div class="p-5 md:py-[50px]">
    <div class="custom-container flex flex-col space-y-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center justify-start space-x-3" @click="backTo">
          <IconsLeftArrow class="w-5 h-5 md:w-6 md:h-6 cursor-pointer" />
          <span class="text-[18px] font-semibold line-clamp-1">
            {{ $t("my_cart") }}
          </span>
        </div>
      </div>
      <WarningModal
        v-if="showWarningModal"
        :showWarningModal="showWarningModal"
        @closeAppModal="showWarningModal = false"
      />
    </div>
    <!-- Steps -->
    <div class="w-full lg:w-3/5 mx-auto mt-8">
      <div class="px-10 lg:px-0">
        <ol class="flex items-center">
          <li
            class="flex relative w-full items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
          >
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                :class="
                  !goToBilling && !forceToLogin
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200 '
                "
                class="flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0"
                >1</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("order_confirmation") }}
              </span>
            </div>
          </li>
          <li
            v-if="!tokenCookie"
            class="flex relative w-full items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
          >
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                :class="forceToLogin ? 'bg-red-600 text-white' : 'bg-gray-200 '"
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >2</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("login") }}
              </span>
            </div>
          </li>
          <li v-if="!tokenCookie" class="relative flex items-center">
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >3</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("payment") }}
              </span>
            </div>
          </li>
          <li
            v-if="tokenCookie"
            class="flex relative w-full items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
          >
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                :class="
                  goToBilling && paymentStep === 'billing'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200'
                "
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >2</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("verification") }}
              </span>
            </div>
          </li>
          <li
            v-if="tokenCookie"
            class="flex relative w-full items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
          >
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                :class="
                  goToBilling && paymentStep === 'manualPayment'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200'
                "
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >3</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("payment") }}
              </span>
            </div>
          </li>

          <li v-if="tokenCookie" class="relative flex items-center">
            <div class="w-auto relative text-center whitespace-nowrap">
              <span
                :class="
                  goToBilling && paymentStep === 'status'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200'
                "
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >4</span
              >
              <span
                class="absolute top-14 left-1/2 -translate-x-1/2 font-semibold text-black text-base ext-base md:text-[20px]"
              >
                {{ $t("status") }}
              </span>
            </div>
          </li>
        </ol>
      </div>
    </div>

    <!-- Order Confirmation Section -->
    <div
      v-if="!goToBilling"
      class="custom-container flex flex-col lg:flex-row justify-between gap-4 md:gap-10 xl:gap-16"
    >
      <!-- Course Details -->
      <div
        v-for="product in cart"
        class="flex flex-col gap-5 w-full lg:w-1/2 mt-24"
      >
        <div class="flex gap-5 border-2 w-full py-8 px-5 h-[140px]">
          <img
            v-if="
              (product.item?.banner_url || product.banner_url) &&
              product.item?.banner_url !== 'null' &&
              product.banner_url !== 'null'
            "
            class="w-[130px] h-[75px] object-cover"
            :src="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.banner_url
                : product.banner_url
            "
            :alt="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.title
                : product.title
            "
          />
          <img
            v-else
            class="w-[80px] object-cover aspect-[10/9]"
            src="/images/course-details/courseIcon.png"
            :alt="
              tokenCookie !== '' && tokenCookie !== undefined
                ? product.item?.title
                : product.title
            "
          />
          <div class="">
            <p
              class="text-lg md:text-[24px] font-semibold text-primary-red line-clamp-2"
            >
              {{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product.item?.title
                  : product.title
              }}
            </p>
            <p
              v-if="
                product?.item?.special_price > 0 || product?.special_price > 0
              "
              class="mr-2 text-lg md:text-[24px] text-gray-600"
            >
              ৳{{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product?.item?.special_price
                  : product?.special_price
              }}
            </p>
          </div>
        </div>
        <div
          v-if="subcriptionInfo && subcriptionInfo[1]"
          class="subscription border-2 w-full py-8 px-5 word-break"
          v-html="subcriptionInfo[1]"
        ></div>
      </div>

      <!-- Course Price -->
      <div v-if="!forceToLogin" class="w-full lg:w-1/2">
        <div v-for="product in cart" class="border-2 py-8 px-5 mt-24">
          <div class="flex justify-between">
            <p class="text-xl md:text-2xl font-semibold">
              {{
                tokenCookie !== "" && tokenCookie !== undefined
                  ? product?.defaultsubscription?.name
                  : product?.defaultSubcription?.name
              }}
            </p>
            <div class="flex text-xl md:text-2xl font-semibold">
              <p
                v-if="
                  product?.item?.special_price > 0 || product?.special_price > 0
                "
                class="mr-2"
              >
                ৳{{
                  tokenCookie !== "" && tokenCookie !== undefined
                    ? product?.item?.special_price
                    : product?.special_price
                }}
              </p>
              <p
                :class="
                  product?.item?.special_price > 0 || product?.special_price > 0
                    ? 'line-through'
                    : ``
                "
              >
                <span
                  v-if="
                    product?.item?.special_price === 0 ||
                    product?.special_price === 0
                  "
                  >৳</span
                >{{
                  tokenCookie !== "" && tokenCookie !== undefined
                    ? product.item?.price
                    : product?.price
                }}
              </p>
            </div>
          </div>
          <div
            v-if="!applyCouponInput"
            @click="applyCouponInput = !applyCouponInput"
            class="flex justify-between items-center cursor-pointer my-6"
          >
            <p class="font-semibold text-primary-red text-[24px]">
              {{ $t("apply_coupon_here") }}
            </p>
            <div
              class="py-1.5 px-4 rounded-lg border border-gray-300 flex justify-center items-center text-base font-medium text-center"
            >
              <span>{{ $t("click_here_btn") }}</span>
            </div>
          </div>
          <div v-if="applyCouponInput && !isCouponAccepted">
            <div>
              <input
                v-model="applyCoupon"
                :placeholder="$t('apply_coupon_here')"
                class="border-2 mt-4 px-4 border-black rounded-[10px] w-full h-[50px]"
                type="text"
              />
            </div>
            <div class="flex justify-between pt-[10px] pb-4">
              <button
                @click="applyCouponInput = !applyCouponInput"
                class="border-2 text-base md:text-[20px] border-black h-[50px] w-[210px]"
              >
                {{ $t("cancel") }}
              </button>
              <button
                @click="handleApplyCoupon"
                :disabled="isProcessing"
                class="bg-black text-white h-[50px] w-[210px] text-base md:text-[20px]"
              >
                {{ $t("submit") }}
              </button>
            </div>
          </div>
          <div v-if="isCouponAccepted" class="flex mt-5 justify-between">
            <p class="text-base md:text-[20px]">
              {{ $t("promo_code") }}: <span class="">{{ applyCoupon }}</span>
            </p>
            <p class="text-base md:text-[20px] text-primary-red">
              - ৳{{ $bdNumberFormat(couponDiscount) }}
            </p>
          </div>

          <div class="flex mt-5 justify-between items-center">
            <div class="flex items-center">
              <p class="text-base md:text-[20px]">
                {{ $t("course_duration") }}
              </p>

              <div v-if="tokenCookie !== '' && tokenCookie !== undefined">
                <div class="relative">
                  <select
                    v-if="product.subscription?.id"
                    class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                    id="selectedSubscription"
                    name="SelectedSubscription"
                    v-model="product.subscription.id"
                    @change="updateSubscription(product.id)"
                  >
                    <option selected :value="product?.defaultsubscription?.id">
                      {{ $t("select") }}
                    </option>
                    <option
                      v-for="(option, index) in allSubscriptions"
                      :key="index"
                      :value="option.id"
                    >
                      {{ option?.durations_as_days }}
                      {{
                        option?.durations_as_days > 1 ? $t("days") : $t("day")
                      }}
                    </option>
                  </select>

                  <IconsCaretDown
                    class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                  />
                </div>
              </div>
              <div v-else>
                <div class="relative">
                  <select
                    class="appearance-none ml-4 px-2 h-10 min-w-[120px] border border-black-700 outline-none bg-white rounded-md cursor-pointer"
                    id="selectedSubscription"
                    name="SelectedSubscription"
                    v-model="product.subscription_id"
                    @change="updateSubscription(product.id)"
                  >
                    <option selected :value="product?.defaultSubcription.id">
                      {{ $t("select") }}
                    </option>
                    <option
                      v-for="(option, index) in allSubscriptions"
                      :key="index"
                      :value="option.id"
                    >
                      {{ option?.durations_as_days }}
                      {{
                        option?.durations_as_days > 1 ? $t("days") : $t("day")
                      }}
                    </option>
                  </select>

                  <IconsCaretDown
                    class="absolute top-0 bottom-0 my-auto right-2 text-black w-3 cursor-pointer"
                  />
                </div>
              </div>
            </div>
            <template
              v-if="
                product?.defaultsubcription?.id !== product?.subscription?.id
              "
            >
              <div
                v-if="product?.subscriptionTotal"
                class="text-base md:text-[20px]"
              >
                ৳{{ product?.subscriptionTotal.toFixed(2) }}
              </div>
            </template>
            <template
              v-if="
                product?.defaultSubcription?.id !== product?.subscription_id
              "
            >
              <div class="text-base md:text-[20px]">
                ৳{{
                  getSubscriptionAmount(product.subscription_id)?.toFixed(2)
                }}
              </div>
            </template>
          </div>
          <div v-if="false" class="flex justify-between items-center mt-5">
            <p class="text-base md:text-[20px]">{{ $t("monthly_cost") }}</p>
            <p v-if="tokenCookie !== '' && tokenCookie !== undefined">
              {{ product?.subscription?.durations_as_month }} x ৳{{
                product?.item?.monthly_service_charge
              }}
            </p>
            <p v-else>
              {{ getSubscriptionDuration(product?.subscription_id) }} x ৳{{
                product.monthly_service_charge
              }}
            </p>
          </div>
          <div
            class="flex justify-between flex-row w-full py-2 border-t border-black my-[20px]"
          >
            <p class="font-semibold text-base md:text-[20px]">
              {{ $t("total") }}
            </p>
            <p class="font-semibold text-base md:text-[20px]">
              ৳{{ afterAppliedCoupon(totalPrice, couponDiscount) }}
            </p>
          </div>
          <div>
            <button
              @click="processToCheckout"
              :disabled="loadingCart"
              class="bg-black text-white h-[50px] w-full text-base md:text-[20px]"
              :class="loadingCart ? 'opacity-50' : ''"
            >
              {{ $t("get_started") }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="forceToLogin" class="w-full lg:w-1/2 py-8 md:px-5 mt-16">
        <AuthLogin
          @loggedInSuccess="setForceToLogin"
          @signupSuccess="setForceToLogin"
          class="flex flex-col items-center justify-center"
        />
      </div>
    </div>
    <div v-else>
      <Billing
        v-if="paymentStep === 'billing'"
        :currentBillingComp="currentBillingComp"
        @goToManualPayment="paymentStep = 'manualPayment'"
      />
      <div
        v-if="paymentStep === 'manualPayment'"
        class="w-full md:w-4/5 xl:w-2/3 mx-auto pt-20"
      >
        <ManualPayment
          :banks="banks"
          :currentBillingComp="currentBillingComp"
          :totalPrice="afterAppliedCoupon(totalPrice, couponDiscount)"
          @submitPayment="submitPayment"
        />
      </div>
      <div v-if="paymentStep === 'status'" class="h-80">
        <div
          class="payment_box p-[24px] md:p-[32px] xl:px-[83px] xl:py-[70px] mt-[100px] max-w-[644px] mx-auto"
        >
          <p class="text-xl text-center">
            {{
              $t(
                "Your_course_enrollment_is_in_process_You_will_receive_a_confirmation_message_after_it_is_completed"
              )
            }}
          </p>
        </div>
      </div>
    </div>

    <div
      v-if="cart && cart.length === 0 && paymentStep != 'status'"
      class="w-full h-full flex flex-col justify-center items-center pt-[140px]"
    >
      <IconsNoData />
      <p class="text-lg pt-5">{{ $t("no_data_text") }}</p>
    </div>
  </div>
</template>

<style scoped>
th,
td {
  @apply text-left min-w-[80px] text-base lg:text-lg border border-black py-2 lg:py-3 px-5;
}
.has-tooltip {
  @apply relative;
}
.tooltip {
  @apply absolute bg-primary-red text-white z-[100] left-20 -top-[56px] lg:-top-24 text-left invisible p-1.5 px-4 rounded-xl  shadow-lg w-full min-w-[300px] whitespace-normal break-words;
}

.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.word-break {
  word-wrap: break-word;
}
.payment_box {
  border-radius: 8px;
  border: 0.5px solid #eff0f6;
  background: #fff;
  box-shadow: 0px 98px 66px 0px rgba(255, 110, 31, 0.02),
    0px 1px 104px 0px rgba(255, 110, 31, 0.04),
    0px 54px 54px 0px rgba(255, 110, 31, 0.02);
}
</style>

<script setup>
const route = useRoute();
const router = useRouter();
const localePath = useLocalePath();
const { setRedirectUrl } = useAuth();
const tokenCookie = useCookie("token");
const { SUCCESS_MESSAGE } = useUrls();
const isLoading = ref(true);
const orderHistory = ref(null);
if (!tokenCookie.value) {
  setRedirectUrl(route.fullPath);
  router.push("/auth/login");
}

onMounted(() => {
  if (tokenCookie.value) {
    setTimeout(async () => {
      isLoading.value = true;
      try {
        const { data, pending } = await useLazyFetch(
          `${SUCCESS_MESSAGE}?payment_success_token=${route.query.payment_success_token}`,
          {
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
          }
        );
        if (!pending.value) {
          orderHistory.value = data.value.order;
          isLoading.value = false;
        }
      } catch (error) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
        isLoading.value = false;
      } finally {
        isLoading.value = false;
      }
    }, 2000);
  }
});
</script>

<template>
  <div class="w-full px-5 bg-[#ff6e1f0a] pt-[40px] pb-[120px]">
    <Transition name="fade">
      <div
        v-if="isLoading"
        class="w-full flex flex-col h-[600px] justify-center items-center pt-20"
      >
        <IconsIsLoading />
      </div>
      <div
        v-else-if="!isLoading && orderHistory"
        class="payment_box flex flex-col p-8 pt-10 max-w-[660px] mx-auto text-gray-800 bg-white"
      >
        <div class="w-full flex flex-col text-center">
          <IconsTick class="mx-auto w-16 h-16 lg:w-20 lg:h-20" />
          <h4 class="text-2xl lg:text-[32px] font-bold pt-7">
            {{ $t("congratulations") }}
          </h4>
          <p
            class="max-w-[472px] mx-auto text-lg lg:text-xl font-normal pt-4 block"
          >
            {{
              $t(
                "Your_payment_completed_successfully_check_SMS_or_email_for_receipt"
              )
            }}
          </p>
        </div>
        <ul
          class="text-sm sm:text-base lg:text-xl font-normal pt-[52px] sm:space-y-5 space-y-3"
        >
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Order_Id") }}</p>
            <p class="">{{ orderHistory.id }}</p>
          </li>
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Order_Type") }}</p>
            <p class="">{{ orderHistory.order_type }}</p>
          </li>
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Total_Price") }}</p>
            <p class="">৳{{ orderHistory.total }}</p>
          </li>
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Transaction_ID") }}</p>
            <p class="">{{ orderHistory.transaction_id }}</p>
          </li>
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Account_Name") }}</p>
            <p class="">{{ orderHistory.user.name }}</p>
          </li>
          <li class="flex sm:flex-row flex-col items-center sm:justify-between">
            <p class="font-medium">{{ $t("Account_Email") }}</p>
            <p class="">{{ orderHistory.user.email }}</p>
          </li>
          <li
            class="flex sm:flex-row flex-col sm-space-y-0 space-y-3 items-center sm:justify-between pt-3"
          >
            <p class="font-bold">
              {{
                orderHistory.order_type === "file"
                  ? $t("Download_file_from_enrolled_course")
                  : $t("Start_the_course_of_your_choice")
              }}
            </p>
            <NuxtLink
              :to="localePath('/dashboard/enrolled-courses')"
              class="h-10 lg:h-[50px] !mt-0 flex items-center justify-between bg-primary-red text-white rounded-[5px] px-5"
            >
              <span>{{ $t("Get_started") }}</span>
              <IconsCaretDown class="-rotate-90 ml-5 w-4" />
            </NuxtLink>
          </li>
        </ul>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.payment_box {
  border-radius: 19px;
  border: 1px solid #eff0f6;
}
</style>

<script setup>
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

const { PAGE_OVERVIEW_SECTION, COMMUNICATIONS, GET_ALL_DISTRICTS } = useUrls();

const showDistricts = ref(false);
const selectedDistrict = ref({});
const searchInput = ref(null);
const searchInputText = ref("");
const singleDistrictData = ref(null);
const isProcessing = ref(false);
const { $toast } = useNuxtApp();
const selectedItem = ref(null);

const { data: pageOverViewRes } = await useFetch(
  `${PAGE_OVERVIEW_SECTION}/communication`
);
const pageOverView = computed(() => pageOverViewRes.value?.data);

const districts = ref("");

const fetchSingleDistrict = async (districtId) => {
  isProcessing.value = true;

  try {
    const data = await $fetch(`${COMMUNICATIONS}/${districtId ?? "null"}`);

    singleDistrictData.value = data?.data ?? null;
  } catch (error) {
    if (error?.statusCode === 404) {
      singleDistrictData.value = null;
    } else {
      singleDistrictData.value = null;
      $toast("clear");
      $toast("error", {
        message: error?.message ?? "Something went wrong",
        className: "toasted-bg-success",
      });
    }
  } finally {
    isProcessing.value = false;
  }
};

const setSelectedDistrict = async (district) => {
  selectedItem.value = null;
  selectedDistrict.value = district;
  await fetchSingleDistrict(district.id);
};

await setSelectedDistrict({});

const getAllDistricts = async () => {
  isProcessing.value = true;

  try {
    const data = await $fetch(GET_ALL_DISTRICTS);

    if (data?.data) {
      data.data.shift();

      districts.value = data.data;

      await fetchSingleDistrict();
    } else {
      districts.value = [];
      $toast("clear");
      $toast("error", {
        message: "No districts found",
        className: "toasted-bg-success",
      });
    }
  } catch (error) {
    $toast("clear");
    $toast("error", {
      message: error?.message ?? "Failed to fetch districts",
      className: "toasted-bg-success",
    });
    districts.value = [];
  } finally {
    isProcessing.value = false;
  }
};

await getAllDistricts();

const filteredDistricts = computed(() => {
  if (districts.value) {
    return districts.value.filter((district) =>
      district.name.toLowerCase().includes(searchInputText.value.toLowerCase())
    );
  }
});
const toggleDistricts = () => {
  showDistricts.value = !showDistricts.value;
  if (showDistricts.value) {
    setTimeout(() => {
      searchInput.value.focus();
    }, 100);
  } else {
    searchInputText.value = "";
  }
};

const { data: howFromIndiaRes } = await useFetch(`${COMMUNICATIONS}/65`);
const howFromIndia = computed(() => howFromIndiaRes.value?.data);

const pageOverViewMedia = ref(false);
const fromIndiaVideo = ref(false);

const setItem = (id) => {
  if (selectedItem.value === id) {
    selectedItem.value = null;
  } else {
    selectedItem.value = id;
  }
};
const isSelected = (id) => {
  return selectedItem.value === id;
};

onMounted(() => {
  window.scrollTo(0, 0);
});
</script>

<template>
  <main class="custom-container pt-10">
    <div
      v-if="pageOverView"
      class="flex flex-col-reverse md:flex-row items-center gap-10 xl:gap-20"
    >
      <div class="w-full md:w-1/2 h-full">
        <p
          v-if="pageOverView.sub_title"
          class="text-sm md:text-base lg:text-lg pb-4"
        >
          {{ pageOverView.sub_title }}
        </p>
        <h1
          v-if="pageOverView.title"
          class="text-primary-red font-bold text-[32px] md:text-[40px] xl:text-[60px] pb-4 mt-0 break-words"
        >
          {{ pageOverView.title }}
        </h1>
        <div
          class="text-base md:text-2xl text-justify"
          v-html="pageOverView.content"
        ></div>
      </div>

      <div v-if="pageOverView.media_link" class="w-full md:w-1/2 h-full">
        <div v-if="pageOverView.type === 'video'" class="bg-[#15151F]">
          <Transition name="page" mode="out-in">
            <div v-if="!pageOverViewMedia" class="relative">
              <img
                class="w-full aspect-video z-[4]"
                :src="
                  pageOverView?.cover_image
                    ? pageOverView?.cover_image
                    : defaultThumb
                "
                alt=""
              />
              <div
                class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
              >
                <div
                  @click="pageOverViewMedia = true"
                  class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                >
                  <IconsPlay class="w-[18px] aspect-[3/4]" />
                </div>
              </div>
            </div>
            <iframe
              v-else
              class="w-full aspect-video"
              :src="`${pageOverView.media_link}?rel=0&autoplay=1`"
              frameborder="0"
              allowfullscreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            ></iframe>
          </Transition>
        </div>
        <img
          v-else
          :src="pageOverView.media_link"
          class="w-full aspect-video object-cover"
          :alt="pageOverView.title"
        />
      </div>
    </div>
    <NoPageFound v-else minHeight="420" />

    <iframe
      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3623.9941865808214!2d88.16752707613328!3d24.727079850580207!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39fbad05447dfcab%3A0xa3e2a1c730c2be54!2sSK%20INFO%20BD!5e0!3m2!1sen!2sbd!4v1697220026666!5m2!1sen!2sbd"
      class="mt-20 w-full h-[480px] md:h-[400px] xl:h-[515px]"
      style="border: 0"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade"
    ></iframe>

    <section class="pt-20">
      <h2 class="text-3xl text-[#EC1F27] font-bold">
        {{ $t("districts_title") }}
      </h2>

      <IconsIsLoading v-if="isProcessing" class="h-24" />
      <p v-if="isProcessing" class="text-lg tex-center flex justify-center">
        {{ $t("loading") }}
      </p>

      <div v-if="!isProcessing">
        <div
          class="pt-10 w-full flex flex-col md:flex-row items-start justify-between gap-10 md:gap-6 lg:gap-10 xl:gap-28"
        >
          <div class="w-full" :class="singleDistrictData ? 'md:w-1/2' : ''">
            <div class="relative w-48">
              <div
                @click="toggleDistricts"
                class="z-[2] relative flex items-center justify-between px-4 w-full h-[50px] outline-none bg-primary-red text-white rounded-t-2xl cursor-pointer"
                :class="showDistricts ? 'rounded-b-0' : 'rounded-b-2xl'"
              >
                <span class="font-semibold">{{
                  selectedDistrict?.name ? selectedDistrict.name : "Random Data"
                }}</span>
                <IconsChevronDown
                  class="transition-all duration-500 ease-in-out w-5 text-white"
                  :class="showDistricts ? 'rotate-180' : 'rotate-0 '"
                />
              </div>
              <div
                v-if="showDistricts"
                @click="toggleDistricts"
                class="fixed top-0 right-0 left-0 bottom-0 z-[1]"
              ></div>
              <div
                v-if="showDistricts"
                class="w-full h-auto absolute top-12 left-0 z-[2] bg-white rounded-b-2xl shadow-md"
              >
                <input
                  ref="searchInput"
                  class="w-full h-[50px] outline-none border border-[#0000007e] px-4"
                  type="text"
                  :placeholder="$t('search')"
                  v-model="searchInputText"
                />
                <div class="pb-8 bg-[#F1F1F1] rounded-b-2xl">
                  <div class="h-auto max-h-[300px] px-4 overflow-y-auto">
                    <div
                      v-for="(district, index) in filteredDistricts"
                      :key="district.id"
                      class="pt-3.5"
                    >
                      <span
                        class="cursor-pointer pr-4"
                        @click="setSelectedDistrict(district)"
                        >{{ district.name }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-if="singleDistrictData"
              class="text-lg pt-11"
              v-html="singleDistrictData?.description"
            ></div>
            <NoPageFound v-else minHeight="420" />
          </div>
          <div
            v-if="singleDistrictData"
            class="w-full md:w-1/2 h-[320px] xl:h-[400px] 3xl:h-[480]"
          >
            <img
              v-if="singleDistrictData?.media_type === 'image'"
              class="w-full aspect-video object-cover"
              :src="singleDistrictData?.media_link"
              :alt="singleDistrictData?.title"
            />
            <div
              v-if="singleDistrictData?.media_type === 'video'"
              class="bg-[#15151F]"
            >
              <Transition name="page" mode="out-in">
                <div v-if="!isSelected(singleDistrictData.id)" class="relative">
                  <img
                    class="w-full aspect-video z-[4]"
                    :src="
                      singleDistrictData?.cover_image
                        ? singleDistrictData?.cover_image
                        : defaultThumb
                    "
                    alt=""
                  />
                  <div
                    class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
                  >
                    <div
                      @click="setItem(singleDistrictData.id)"
                      class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                    >
                      <IconsPlay class="w-[18px] aspect-[3/4]" />
                    </div>
                  </div>
                </div>
                <iframe
                  v-else
                  class="w-full aspect-video"
                  :src="`${singleDistrictData.media_link}?rel=0&autoplay=1`"
                  frameborder="0"
                  allowfullscreen
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                ></iframe>
              </Transition>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div
      v-if="howFromIndia"
      class="pt-5 pb-10 md:py-20 lg:py-28 xl:py-[150px] w-full flex flex-col-reverse md:flex-row items-start justify-between gap-10 md:gap-6 lg:gap-10 xl:gap-28"
    >
      <div class="w-full md:w-1/2">
        <img
          v-if="howFromIndia?.media_type === 'image'"
          class="w-full object-cover aspect-video"
          :src="howFromIndia?.media_link"
          :alt="howFromIndia?.title"
        />
        <div v-if="howFromIndia?.media_type === 'video'" class="bg-[#15151F]">
          <Transition name="page" mode="out-in">
            <div v-if="!fromIndiaVideo" class="relative">
              <img
                class="w-full aspect-video z-[4]"
                :src="
                  howFromIndia?.cover_image
                    ? howFromIndia?.cover_image
                    : defaultThumb
                "
                alt=""
              />
              <div
                class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
              >
                <div
                  @click="fromIndiaVideo = true"
                  class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                >
                  <IconsPlay class="w-[18px] aspect-[3/4]" />
                </div>
              </div>
            </div>
            <iframe
              v-else
              class="w-full aspect-video"
              :src="`${howFromIndia.media_link}?rel=0&autoplay=1`"
              frameborder="0"
              allowfullscreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            ></iframe>
          </Transition>
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <h2 class="text-3xl text-[#EC1F27] font-bold">
          {{ howFromIndia?.title }}
        </h2>
        <div v-html="howFromIndia?.description" class="text-lg pt-11"></div>
      </div>
    </div>
    <NoPageFound v-else minHeight="420" />
  </main>
</template>

<style scoped>
.card-shadow {
  box-shadow: 0px -4px 4px 0px #00000040;
}
.provider-card-top-shadow {
  box-shadow: 0px -4px 4px 0px #00000040, 0px 4px 4px 0px #00000040;
}
</style>

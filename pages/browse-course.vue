<script setup lang="ts">
import defaultThumb from "~/assets/img/default/video-thumbnail.webp";

const { apiBaseUrl, PAGE_OVERVIEW_SECTION } = useUrls();
const localePath = useLocalePath();

const { data: pageOverViewRes } = await useFetch<any>(
  `${PAGE_OVERVIEW_SECTION}/browse-course`
);
const pageOverView = computed(() => pageOverViewRes.value?.data);

const courseCurrentPage = ref(1);
const courseTotalPages = ref(1);
const perPageCourses = ref(9);
const currentCourses = ref(<any>[]);

const isLastPage = computed(() => {
  return courseCurrentPage.value === courseTotalPages.value;
});

const getCourses = async () => {
  const { data, pending, error } = await useFetch<any>(
    `${apiBaseUrl}/courses?per_page=${perPageCourses.value}&page=${courseCurrentPage.value}`
  );
  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        currentCourses.value.push(...data.value.data);
        courseTotalPages.value = data.value.meta.last_page;
      } else if (error.value) {
        console.log(error.value);
      }
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};

await getCourses();

const loadMoreCourse = async () => {
  if (isLastPage.value) {
    currentCourses.value = currentCourses.value.slice(0, perPageCourses.value);
    courseCurrentPage.value = 1;
  } else {
    courseCurrentPage.value++;
    await getCourses();
  }
};

const pageOverViewMedia = ref(false);

onMounted(() => {
  window.scrollTo(0, 0);
});
</script>

<template>
  <main class="custom-container">
    <section class="pt-10 pb-20">
      <div
        v-if="pageOverView"
        class="flex flex-col-reverse md:flex-row items-center gap-10 xl:gap-20"
      >
        <div class="w-full md:w-1/2 h-full">
          <p
            v-if="pageOverView.sub_title"
            class="text-sm md:text-base lg:text-lg pb-4"
          >
            {{ pageOverView.sub_title }}
          </p>
          <h1
            v-if="pageOverView.title"
            class="text-primary-red font-bold text-[32px] md:text-[40px] xl:text-[60px] pb-4 mt-0 break-words"
          >
            {{ pageOverView.title }}
          </h1>
          <div
            class="text-base md:text-2xl text-justify"
            v-html="pageOverView.content"
          ></div>
        </div>

        <div v-if="pageOverView.media_link" class="w-full md:w-1/2 h-full">
          <div v-if="pageOverView.type === 'video'" class="bg-[#15151F]">
            <Transition name="page" mode="out-in">
              <div v-if="!pageOverViewMedia" class="relative">
                <img
                  class="w-full aspect-video z-[4]"
                  :src="
                    pageOverView?.cover_image
                      ? pageOverView?.cover_image
                      : defaultThumb
                  "
                  alt=""
                />
                <div
                  class="absolute top-0 inset-0 m-auto z-[5] flex justify-center items-center"
                >
                  <div
                    @click="pageOverViewMedia = true"
                    class="cursor-pointer flex justify-center items-center animate-button w-14 aspect-square rounded-full pl-1 text-white"
                  >
                    <IconsPlay class="w-[18px] aspect-[3/4]" />
                  </div>
                </div>
              </div>
              <iframe
                v-else
                class="w-full aspect-video"
                :src="`${pageOverView.media_link}?rel=0&autoplay=1`"
                frameborder="0"
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              ></iframe>
            </Transition>
          </div>
          <img
            v-else
            :src="pageOverView.media_link"
            class="object-cover w-full aspect-video"
            :alt="pageOverView.title"
          />
        </div>
      </div>
      <NoPageFound v-else minHeight="420" />
    </section>

    <section class="pt-5 pb-20">
      <div class="flex items-center justify-center">
        <NuxtLink :to="localePath('/demo-class')">
          <div class="relative w-[370px] md:w-[490] xl:w-[590px] h-auto">
            <IconsDemoFolder class="w-full h-full" />
            <h2
              class="absolute inset-x-0 inset-y-[15%] flex items-center justify-center -top-[50px] text-white w-full text-xl md:text-2xl xl:text-6xl"
            >
              {{ $t("demo_class") }}
            </h2>
          </div>
        </NuxtLink>
      </div>
    </section>

    <section class="flex flex-col pt-5 pb-20 items-center justify-center">
      <div
        v-if="currentCourses.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-5 gap-y-6"
      >
        <div
          class="w-full max-w-[410px]"
          v-for="course in currentCourses"
          :key="course.id"
        >
          <img
            v-if="course.banner_url && course.banner_url !== 'null'"
            class="w-full h-[286px] aspect-auto"
            :src="course.banner_url"
            alt="course.title"
          />
          <img
            v-else
            class="w-full h-[286px] aspect-auto"
            src="/images/course-details/courseIcon.png"
            alt="course.title"
          />
          <div class="w-full text-center space-y-3">
            <h3 class="text-2xl font-semibold text-center my-4 line-clamp-2">
              {{ course.title }}
            </h3>
            <NuxtLink
              class="px-4 py-1.5 border border-black text-[#EC1F27] rounded-full"
              :to="localePath(`/precheckout/${course.slug}`)"
            >
              {{ $t("view_course") }}
            </NuxtLink>
          </div>
        </div>
      </div>
      <NoPageFound v-else minHeight="220" />
      <div
        v-if="courseTotalPages > 1"
        class="flex items-center justify-center mt-20 mb-10"
      >
        <button
          class="inline-block rounded-full hover:text-white hover:border-primary-red hover:bg-primary-red text-primary-red opacity-100 px-5 py-2 text-base font-bold leading-normal transition-all duration-150 ease-in-out border border-black"
          @click="loadMoreCourse"
        >
          {{ !isLastPage ? $t("load_more") : $t("view_less") }}
        </button>
      </div>
    </section>

    <section class="pt-5 pb-20 flex items-center justify-center">
      <div class="flex flex-col items-center space-y-3 w-[700px]">
        <h4
          class="text-[32px] md:text-[40px] xl:text-6xl font-bold text-center text-[#EC1F27]"
        >
          {{ $t("comments") }}
        </h4>
        <p class="text-sm md:text-xl xl:text-2xl font-normal text-center">
          {{ $t("comment_text") }}
        </p>
      </div>
    </section>
  </main>
</template>

<style scoped>
.card-shadow {
  box-shadow: 0px -4px 4px 0px #00000040;
}

.provider-card-top-shadow {
  box-shadow: 0px -4px 4px 0px #00000040, 0px 4px 4px 0px #00000040;
}
</style>

<script setup>
import { storeToRefs } from "pinia";
import { useCartDataStore } from "~/stores/cartData";
import { useSubscriptionPackageStore } from "~/stores/subscriptionPackage";
const {
  apiBaseUrl,
  USER_SUBSCRIPTIONS,
  DOWNLOAD_FILES,
  FIRMWARE_FILES,
  siteUlr,
} = useUrls();
const localePath = useLocalePath();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const tokenCookie = useCookie("token");
const { setRedirectUrl } = useAuth();
const { setSubscriptionDataForApi } = useCartDataStore();
const showWarningModal = ref(false);
const currentBillingComp = ref("");

const { isActiveSubscription } = storeToRefs(useSubscriptionPackageStore());

const isLoading = ref(true);
const fileNeedsToBuy = ref(false);
const forceToLogin = ref(false);
const goToBilling = ref(false);

const fileDetail = ref({});

const getFileDetails = async () => {
  const { data, pending, error } = await useFetch(
    `${FIRMWARE_FILES}/${route.params.slug}/details`,
    {
      headers: {
        Authorization: `Bearer ${tokenCookie.value ? tokenCookie.value : ""}`,
      },
    }
  );
  const setData = () => {
    if (!pending.value) {
      if (data.value) {
        fileDetail.value = data.value;
      } else if (error.value) {
        nuxtApp.$toast("clear");
        nuxtApp.$toast("error", {
          message: error.value?.data.message,
          className: "toasted-bg-success",
        });
        isProcessing.value = false;
      }
    } else {
      setTimeout(() => {
        setData();
      }, 300);
    }
  };
  setData();
};
await getFileDetails();
const getStarted = () => {
  currentBillingComp.value = "file";
  goToBilling.value = true;
};

const fileUrl = computed(() => {
  return `${siteUlr}/download/${fileDetail?.value?.data?.slug}`;
});

const convertMBtoGBandMB = (fileSizeMB) => {
  const GB = Math.floor(fileSizeMB / 1024);
  const remainingMB = Math.round((fileSizeMB % 1024) * 100) / 100;

  if (GB === 0) {
    return remainingMB + "MB";
  } else {
    return GB + "." + remainingMB + "GB";
  }
};
const checkDownloadCondition = async (
  downloadFile,
  isThisFeatured,
  requestFileSize
) => {
  try {
    isLoading.value = true;
    if (!tokenCookie.value || tokenCookie.value === "") {
      setRedirectUrl(route.fullPath);
      fileNeedsToBuy.value = true;
      forceToLogin.value = true;
      // router.push(localePath("/auth/login"));
      nuxtApp.$toast("clear");
      nuxtApp.$toast("error", {
        message: t("messages.login_first"),
        className: "toasted-bg-alert",
      });
    } else {
      if (isActiveSubscription.value) {
        let response = await $fetch(`${USER_SUBSCRIPTIONS}`, {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        });

        if (
          response.conditions.total_band_width >
            response.conditions.used_total_band_width &&
          response.conditions.total_file > response.conditions.used_total_file
        ) {
          if (
            response.conditions.total_band_width >=
            response.conditions.used_total_band_width + requestFileSize
          ) {
            if (
              response.conditions.is_all_file ||
              (response.conditions.is_featured_file && isThisFeatured) ||
              (response.conditions.is_none_featured_file && !isThisFeatured)
            ) {
              let downloadUrl = await $fetch(
                `${DOWNLOAD_FILES}/${downloadFile.id}/download`,
                {
                  headers: {
                    Authorization: `Bearer ${tokenCookie.value}`,
                  },
                  responseType: "blob",
                }
              );
              isLoading.value = false;
              const link = document.createElement("a");
              link.href = URL.createObjectURL(downloadUrl);

              link.download = "download.zip";
              link.click();

              URL.revokeObjectURL(link.href);
              // window.open(downloadUrl.url, "_blank");
            } else {
              isLoading.value = false;
              setRedirectUrl(route.fullPath);
              router.push(localePath("/subscription"));

              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: t("messages.upgrade_subscription"),
                className: "toasted-bg-alert",
              });
            }
          } else {
            isLoading.value = false;
            setRedirectUrl(route.fullPath);
            router.push(localePath("/subscription"));

            nuxtApp.$toast("clear");
            nuxtApp.$toast("error", {
              message: t("messages.exceeded_download_limit"),
              className: "toasted-bg-alert",
            });
          }
        } else {
          isLoading.value = false;
          setRedirectUrl(route.fullPath);
          router.push(localePath("/subscription"));

          nuxtApp.$toast("clear");
          nuxtApp.$toast("error", {
            message: t("messages.upgrade_subscription"),
            className: "toasted-bg-alert",
          });
        }
      } else {
        const { data, pending, error } = useFetch(
          `${DOWNLOAD_FILES}/${downloadFile.id}/download`,
          {
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
          }
        );

        const setData = () => {
          if (!pending.value) {
            if (data.value && data.value?.file_url_type === "link") {
              window.open(data.value.url, "_blank");
            } else if (data.value && !data.value?.file_url_type) {
              const link = document.createElement("a");
              link.href = URL.createObjectURL(data.value);

              link.download = "download.zip";
              link.click();

              URL.revokeObjectURL(link.href);
              setRedirectUrl("/");
            } else if (error.value) {
              nuxtApp.$toast("clear");
              nuxtApp.$toast("error", {
                message: error.value?.data.message,
                className: "toasted-bg-success",
              });

              const makeSubscriptionDataForApi = {
                type: "file",
                downloadFileDetails: downloadFile,
                downloadFileId: [
                  {
                    item_id: downloadFile.id,
                  },
                ],
              };
              setSubscriptionDataForApi(makeSubscriptionDataForApi);

              if (error.value?.statusCode === 403) {
                setTimeout(() => {
                  // router.push(localePath("/billing"));
                  fileNeedsToBuy.value = true;
                }, 300);
              }
            }
            loadUserFileCourseDetails();
          } else {
            setTimeout(() => {
              setData();
            }, 300);
          }
        };
        setData();
      }
    }
  } catch (error) {
    console.log(error);
    nuxtApp.$toast("clear");
    nuxtApp.$toast("error", {
      message: error?.response?._data?.message,
      className: "toasted-bg-alert",
    });
  }
};

const showFileDetail = ref(false);
const isDownloadAvailable = ref(false);
const loadUserFileCourseDetails = async () => {
  if (tokenCookie.value) {
    try {
      const { data } = await useFetch(
        `${apiBaseUrl}/user/enrollments/file-courses`,
        {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        }
      );
      if (data.value) {
        const isDownloadAvailableData = data.value.data.find(
          (element) =>
            element.item.id === fileDetail.value.data.id && !element.is_expired
        );

        if (isDownloadAvailableData) {
          isDownloadAvailable.value = true;
        } else {
          isDownloadAvailable.value = false;
        }
      }
    } catch (error) {
      console.log(error);
    }
  }
};

const setForceToLogin = () => {
  const makeSubscriptionDataForApi = {
    type: "file",
    downloadFileDetails: fileDetail.value.data,
    downloadFileId: [
      {
        item_id: fileDetail.value.data.id,
      },
    ],
  };
  setSubscriptionDataForApi(makeSubscriptionDataForApi);

  setTimeout(() => {
    loadUserFileCourseDetails();
    forceToLogin.value = false;
  }, 500);
};

const backTo = () => {
  if (!fileNeedsToBuy.value) {
    router.back();
  } else {
    if (goToBilling.value) {
      goToBilling.value = false;
    } else {
      fileNeedsToBuy.value = false;
    }
  }
};

onMounted(() => {
  window.scrollTo(0, 0);
});
</script>

<template>
  <div class="custom-container pt-10 pb-24">
    <div class="flex flex-col space-y-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center justify-start space-x-3" @click="backTo">
          <IconsLeftArrow class="w-5 h-5 md:w-6 md:h-6 cursor-pointer" />
          <span class="text-2xl font-semibold line-clamp-1">
            {{ $t("my_cart") }}
          </span>
        </div>
      </div>
      <WarningModal
        v-if="showWarningModal"
        :showWarningModal="showWarningModal"
        @closeAppModal="showWarningModal = false"
      />
    </div>
    <div v-if="fileNeedsToBuy" class="">
      <div class="w-full px-6 md:w-3/5 mx-auto mt-6">
        <div class="px-2">
          <ol class="flex items-center">
            <li
              :class="tokenCookie ? 'w-full' : 'w-3/5'"
              class="flex relative items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-2 after:inline-block"
            >
              <span
                :class="
                  !goToBilling && !forceToLogin
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-200 '
                "
                class="flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0"
                >1</span
              >
              <p
                class="absolute top-14 left-[-45px] font-semibold text-black text-base md:text-[20px]"
              >
                {{ $t("order_confirmation") }}
              </p>
            </li>
            <li
              v-if="!tokenCookie"
              class="flex relative w-3/5 items-center after:content-[''] after:w-full after:h-1 after:border-dashed after:border-gray-100 after:border-2 after:inline-block"
            >
              <span
                :class="forceToLogin ? 'bg-red-600 text-white' : 'bg-gray-200 '"
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >2</span
              >
              <p
                class="absolute top-14 left-[-5px] font-semibold text-black text-base md:text-[20px]"
              >
                {{ $t("login") }}
              </p>
            </li>
            <li v-if="!tokenCookie" class="flex relative items-center">
              <span
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >3</span
              >
              <p
                class="absolute top-14 left-[-10px] font-semibold text-black text-base md:text-[20px]"
              >
                {{ $t("payment") }}
              </p>
            </li>
            <li v-if="tokenCookie" class="flex relative items-center w-20">
              <span
                :class="!goToBilling ? 'bg-gray-200' : 'bg-red-600 text-white'"
                class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0"
                >2</span
              >
              <p
                class="absolute top-14 left-[-10px] font-semibold text-black text-base md:text-[20px]"
              >
                {{ $t("payment") }}
              </p>
            </li>
          </ol>
        </div>
      </div>
      <div
        v-if="!goToBilling"
        class="flex flex-col lg:flex-row justify-between gap-4 md:gap-10 xl:gap-16"
      >
        <div class="w-full lg:w-1/2">
          <div class="border-2 py-8 px-5 mt-24 h-[140px]">
            <div class="flex items-center">
              <IconsDevice class="w-[68px] h-[84px]" />
              <div class="ml-[30px]">
                <p
                  class="text-lg md:text-xl font-semibold text-primary-red line-clamp-2"
                >
                  {{ fileDetail?.data?.title }}
                </p>
                <p
                  v-if="fileDetail?.data?.special_price"
                  class="mr-2 text-lg md:text-xl text-gray-600"
                >
                  ৳ {{ fileDetail?.data?.special_price }}
                </p>
                <p v-else class="mr-2 text-lg md:text-xl text-gray-600">
                  ৳ {{ fileDetail?.data?.price }}
                </p>
              </div>
            </div>
          </div>
          <div class="border-2 py-8 px-5 mt-6">
            <div class="items-center">
              <p class="text-base md:text-[20px] font-semibold">
                {{ $t("view_file_details") }}
              </p>
              <div
                v-if="fileDetail?.data?.description !== 'null'"
                v-html="fileDetail?.data?.description"
              ></div>
            </div>
          </div>
        </div>
        <div
          v-if="!forceToLogin"
          class="border-2 w-full lg:w-1/2 py-8 px-5 mb-5 mt-24 flex flex-col gap-4"
        >
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("date") }}</p>
            <p class="text-base md:text-[20px]">
              {{ $dateFormat(fileDetail?.data?.created_at) }}
            </p>
          </div>
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("file_size") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              {{ convertMBtoGBandMB(fileDetail?.data?.file_size) }}
            </p>
          </div>
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("file_type") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              {{ fileDetail?.data?.is_featured ? "Featured" : "Non Featured" }}
            </p>
          </div>
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("visits") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              {{ fileDetail?.data?.totalViewCount }}
            </p>
          </div>
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("downloads") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              {{ fileDetail?.data?.totalDownloadCount }}
            </p>
          </div>
          <div class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("price") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              ৳ {{ fileDetail?.data?.price }}
            </p>
          </div>
          <div
            v-if="fileDetail?.data?.special_price"
            class="flex justify-between"
          >
            <p class="text-base md:text-[20px]">{{ $t("special_price") }}</p>
            <p class="text-base md:text-[20px] font-semibold text-primary-red">
              ৳ {{ fileDetail?.data?.special_price }}
            </p>
          </div>
          <div
            v-if="fileDetail?.data?.special_price"
            class="border-t pt-5 border-black flex justify-between"
          >
            <p class="text-base md:text-[20px] font-semibold">
              {{ $t("price") }}
            </p>
            <p class="text-base md:text-[20px] font-semibold">
              ৳ {{ fileDetail?.data?.special_price }}
            </p>
          </div>
          <div v-else class="flex justify-between">
            <p class="text-base md:text-[20px]">{{ $t("price") }}</p>
            <p class="text-base md:text-[20px] font-semibold">
              ৳ {{ fileDetail?.data?.price }}
            </p>
          </div>
          <div>
            <button
              v-if="fileDetail?.data?.is_paid && !isDownloadAvailable"
              @click="getStarted"
              class="bg-black text-white w-full py-2 my-[20px]"
            >
              {{ $t("get_started") }}
            </button>
            <button
              v-else
              class="bg-black text-white w-full py-2 my-[20px]"
              @click="
                checkDownloadCondition(
                  fileDetail?.data,
                  fileDetail?.data?.id,
                  fileDetail?.data?.is_featured,
                  fileDetail?.data?.file_size
                )
              "
            >
              {{ $t("download") }}
            </button>
          </div>
        </div>
        <div v-if="forceToLogin" class="w-full lg:w-1/2 py-8 lg:px-5 mt-16">
          <AuthLogin
            @loggedInSuccess="setForceToLogin"
            @signupSuccess="setForceToLogin"
            class="flex flex-col items-center justify-center"
          />
        </div>
      </div>
      <div v-else>
        <Billing :currentBillingComp="currentBillingComp" />
      </div>
    </div>

    <div
      v-else
      class="mt-4 border border-[#00000048] rounded-[20px] px-4 pt-5 md:pt-10 pb-7 md:pb-[55px]"
    >
      <h2
        v-if="fileDetail?.data?.title"
        class="text-[30px] xl:text-5xl font-extrabold text-center"
      >
        {{ fileDetail?.data?.title }}
      </h2>
      <div
        v-if="fileDetail?.data?.isNew"
        class="flex items-center justify-center py-4 md:py-5 text-center"
      >
        <p
          class="w-16 h-8 md:w-20 md:h-10 bg-[#EC1F27] flex justify-center items-center"
        >
          <span class="text-white font-semibold">New</span>
        </p>
      </div>
      <p
        v-if="fileDetail?.data?.fileName"
        class="text-base md:text-lg font-semibold text-center"
      >
        {{ fileDetail?.data?.fileName }}
      </p>
      <div
        class="py-6 xl:py-10 flex items-center justify-center space-x-5 text-center"
      >
        <div class="flex space-x-2.5">
          <NuxtLink
            :to="`https://www.facebook.com/share.php?u=${fileUrl}`"
            target="_blank"
          >
            <IconsFacebookShare class="w-20 md:w-[100px]" />
          </NuxtLink>
          <div v-if="fileDetail?.data?.fbCount" class="couter-bg w-16 text-sm">
            {{ fileDetail?.data?.fbCount }}
          </div>
        </div>
        <div class="flex space-x-2.5">
          <NuxtLink
            :to="`http://twitter.com/share?&url=${fileUrl}&text=Sk Mobile School firmware - ${fileDetail?.data?.title}.`"
            target="_blank"
          >
            <IconsTwitterShare class="w-20 md:w-[100px]" />
          </NuxtLink>
          <span
            v-if="fileDetail?.data?.twCount"
            class="couter-bg w-16 text-sm"
            >{{ fileDetail?.data?.twCount }}</span
          >
        </div>
      </div>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <p
        v-if="fileDetail?.data?.created_at"
        class="text-sm md:text-xl xl:text-2xl font-semibold py-3 md:py-4 text-center"
      >
        {{ $t("date") }} - {{ $dateFormat(fileDetail?.data?.created_at) }}
      </p>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <p
        v-if="fileDetail?.data?.file_size"
        class="text-sm md:text-xl xl:text-2xl font-semibold py-3 md:py-4 text-center"
      >
        {{ $t("file_size") }} -
        {{ convertMBtoGBandMB(fileDetail?.data?.file_size) }}
      </p>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <p
        v-if="fileDetail?.data?.file_size"
        class="text-sm md:text-xl xl:text-2xl font-semibold py-3 md:py-4 text-center"
      >
        {{ $t("file_type") }} -
        {{ fileDetail?.data?.is_featured ? "Featured" : "Non Featured" }}
      </p>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <p
        class="text-sm md:text-xl xl:text-2xl font-semibold py-3 md:py-4 text-center"
      >
        {{ $t("visits") }} - {{ fileDetail?.data?.totalViewCount }}
      </p>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <p
        class="text-sm md:text-xl xl:text-2xl font-semibold py-3 md:py-4 text-center"
      >
        {{ $t("downloads") }} - {{ fileDetail?.data?.totalDownloadCount }}
      </p>
      <div class="h-0.5 w-full bg-[#00000048]"></div>
      <div
        v-if="fileDetail?.data?.special_price"
        class="flex items-center justify-center text-sm md:text-xl xl:text-2xl font-semibold pt-3 md:pt-4"
      >
        <p class="mr-4">{{ $t("price") }} -</p>
        <span class="line-through text-[#ADA7A7] font-bold">৳</span>
        <span class="line-through text-[#ADA7A7] mr-3">
          {{ fileDetail?.data?.price }}
        </span>
        <p class="">৳</p>
        <p class="">
          {{ fileDetail?.data?.special_price }}
        </p>
      </div>
      <div
        v-else
        class="flex items-center justify-center text-sm md:text-xl xl:text-2xl font-semibold pt-3 md:pt-4"
      >
        <p class="mr-4">{{ $t("price") }} -</p>
        <p class="font-bold">৳</p>
        <p class="">
          {{ fileDetail?.data?.price }}
        </p>
      </div>
      <!-- {{ fileDetail.data }}
      <button class="">cart</button> -->
      <button
        target="_blank"
        @click="
          checkDownloadCondition(
            fileDetail?.data,
            fileDetail?.data?.id,
            fileDetail?.data?.is_featured,
            fileDetail?.data?.file_size
          )
        "
        class="w-full h-10 md:h-[50px] bg-[#EC1F27] text-white font-semibold text-xl xl:text-2xl rounded-md mt-3 md:mt-4"
      >
        {{ $t("download") }}
      </button>
      <button
        class="flex justify-center items-center gap-2 w-full h-10 md:h-[50px] bg-[#EC1F27] text-white font-semibold text-xl xl:text-2xl rounded-md mt-3 md:mt-4"
        @click="showFileDetail = !showFileDetail"
      >
        <span>{{ $t("show_details_btn") }}</span>
        <IconsChevronDown
          class="w-5 h-5 inline-block transition-all duration-500 ease-in-out"
          :class="showFileDetail ? 'rotate-180' : 'rotate-0'"
        />
      </button>
      <div v-if="showFileDetail">
        <div
          v-if="fileDetail?.data?.description"
          class="text-sm md:text-xl pt-3 md:pt-4"
          v-html="fileDetail?.data?.description"
        ></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.couter-bg {
  background-image: url("/icons/speech-bubble.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>

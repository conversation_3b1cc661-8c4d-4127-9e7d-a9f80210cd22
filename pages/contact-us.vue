<script setup>
import { Form, Field, ErrorMessage } from "vee-validate";

const { isRequired, validateEmail, validatePhone } = useValidation();
const { CONTACT_US } = useUrls();
const { $toast } = useNuxtApp();
const localePath = useLocalePath();
const { t } = useI18n();
const config = useRuntimeConfig();
const recaptchaRef = ref(null);

const contactForm = reactive({
  name: "",
  email: "",
  phone: "",
  subject: "Select subject",
  question: "",
  color: "#767474",
  opacity: 50,
});
const subjects = ref([
  { id: 1, value: "Select", label: "Select subject", disabled: true },
  { id: 2, value: "General", label: "General", disabled: false },
  { id: 3, value: "Support", label: "Support", disabled: false },
  { id: 4, value: "Payment", label: "Payment", disabled: false },
]);

const submitForm = async (values, { resetForm }) => {
  if (
    contactForm.name &&
    contactForm.email &&
    contactForm.question &&
    contactForm.phone &&
    contactForm.subject !== "Select subject"
  ) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "CONTACT",
      }
    );
    if (captchaToken) {
      const { data, error } = await useFetch(CONTACT_US, {
        method: "POST",
        body: JSON.stringify({
          name: contactForm.name,
          email: contactForm.email,
          phone: contactForm.phone,
          description: contactForm.question,
          subject: contactForm.subject,
          captcha_token: captchaToken,
        }),
      });
      if (!error.value && data.value) {
        resetForm();
        contactForm.subject = "Select subject"
        $toast("clear");
        $toast("success", {
          message: data.value.message,
          className: "toasted-bg-success",
        });
      } else if (error.value) {
        $toast("clear");
        $toast("error", {
          message: error.value?.data?.message,
          className: "toasted-bg-alert",
        });
      }
    }
  } else {
    $toast("clear");
    $toast("error", {
      message: t("messages.fill_all_fields"),
      className: "toasted-bg-alert",
    });
  }
};

const reloadCaptcha = () => {
  if (window.grecaptcha && window.grecaptcha.enterprise) {
    recaptchaRef.value = window.grecaptcha.enterprise;
  }
};

onMounted(() => {
  watchEffect(() => {
    reloadCaptcha();
  });
  window.scrollTo(0, 0);
});
</script>

<template>
  <section class="custom-container h-full py-24">
    <h1 class="text-center font-semibold text-primary-red text-3xl lg:text-5xl">
      {{ $t("contact_us") }}
    </h1>
    <h3
      class="pt-10 lg:pt-24 text-center lg:text-left text-black text-[32px] font-semibold"
    >
      {{ $t("contact_sub") }}
    </h3>

    <div
      class="w-full h-full flex flex-col lg:flex-row space-y-8 lg:space-y-0 lg:space-x-8 pt-8"
    >
      <Form
        class="w-full flex flex-col space-y-8"
        novalidate
        @submit="submitForm"
        v-slot="{ meta }"
      >
        <div
          class="w-full flex flex-col space-y-8 md:flex-row md:space-x-8 md:space-y-0"
        >
          <div class="w-full md:w-1/2 space-y-1.5">
            <label class="text-black"
              >{{ $t("full_name") }}
              <span class="text-[#ff0000]">*</span></label
            >
            <Field
              id="name"
              type="text"
              name="name"
              class="p-4 w-full rounded-lg shadow-3xl border border-black outline-none"
              :placeholder="$t('name_placeholder')"
              v-model="contactForm.name"
              :rules="isRequired"
            />
            <ErrorMessage class="error-message" name="name" />
          </div>
          <div class="w-full md:w-1/2 space-y-1.5">
            <label class="text-black"
              >{{ $t("email_address") }}
              <span class="text-[#ff0000]">*</span></label
            >
            <Field
              id="email"
              type="email"
              name="email"
              class="p-4 w-full rounded-lg shadow-3xl border border-black outline-none"
              :placeholder="$t('email_placeholder')"
              v-model="contactForm.email"
              :rules="validateEmail"
            />
            <ErrorMessage class="error-message" name="email" />
          </div>
        </div>
        <div
          class="w-full flex flex-col space-y-8 md:flex-row md:space-x-8 md:space-y-0"
        >
          <div class="w-full md:w-1/2 space-y-1.5">
            <label class="text-black"
              >{{ $t("phone_number") }}
              <span class="text-[#ff0000]">*</span></label
            >
            <Field
              id="phone"
              type="text"
              name="phone"
              class="p-4 w-full rounded-lg shadow-3xl border border-black outline-none"
              :placeholder="$t('phone_placeholder')"
              v-model="contactForm.phone"
              :rules="validatePhone"
            />
            <ErrorMessage class="error-message" name="phone" />
          </div>
          <div class="flex flex-col w-full md:w-1/2 space-y-1.5">
            <label for="subject">{{ $t("select_subject") }} </label>
            <div class="w-full rounded-lg select-wrapper cursor-pointer">
              <select
                class="w-full bg-white outline-none p-3 h-[58px] rounded-lg shadow-3xl border border-black relative"
                name="subject"
                id="subject"
                v-model="contactForm.subject"
                :style="{
                  '--text-color':
                    contactForm.subject === 'Select subject'
                      ? `#767474`
                      : `#000`,
                  '--text-opacity':
                    contactForm.subject === 'Select subject' ? `0.5` : `1`,
                }"
              >
                <option
                  v-for="(option, index) in subjects"
                  :key="index"
                  :value="option.label"
                  :disabled="option.disabled"
                >
                  {{ option.label }}
                </option>
              </select>
              <span class="select-toggle absolute">
                <IconsChevronDown class="w-5 text-black" />
              </span>
            </div>
          </div>
        </div>
        <div class="flex flex-col w-full space-y-1.5">
          <label class="text-black"
            >{{ $t("question") }} <span class="text-[#ff0000]">*</span></label
          >
          <Field
            as="textarea"
            id="question"
            name="question"
            class="p-4 w-full outline-none rounded-lg shadow-3xl border border-black resize-none"
            :placeholder="$t('question_placeholder')"
            rows="10"
            v-model="contactForm.question"
            :rules="isRequired"
          ></Field>
          <ErrorMessage class="error-message" name="question" />
        </div>
        <div class="flex justify-start items-center">
          <button
            class="h-14 px-6 md:px-15 text-white rounded-md font-bold text-xl"
            :class="
              !meta.valid ? 'bg-red-300 cursor-not-allowed' : 'bg-primary-red'
            "
            :disabled="!meta.valid"
            type="submit"
          >
            {{ $t("contact_us_btn") }}
          </button>
        </div>
      </Form>

      <div
        class="flex flex-col self-center lg:self-start py-10 appointment-address p-8 xl:p-10 shadow-[0_0px_15px_-6px_rgba(0,0,0,0.5)] rounded-2xl space-y-9 mb-10 lg:mb-0 justify-between"
      >
        <div class="flex flex-col space-y-9">
          <div class="flex space-x-5 md:space-x-6 items-center">
            <img
              class="h-[52px] w-[52px] xl:h-[60px] xl:w-[60px]"
              src="/icons/addresss.png"
              alt=""
            />
            <div class="details flex flex-col space-y-1.5">
              <h4 class="text-xl xl:text-2xl font-medium">
                {{ $t("mailing_address") }}
              </h4>
              <p class="text-base md:text-lg text-light-white break-words">
                {{ $t("address_1") }}<br />
                {{ $t("address_2") }}
              </p>
            </div>
          </div>
          <div class="flex space-x-5 md:space-x-6 items-center">
            <img
              class="h-[52px] w-[52px] xl:h-[60px] xl:w-[60px]"
              src="/icons/email.svg"
              alt=""
            />
            <div class="details flex flex-col">
              <h4 class="text-xl xl:text-2xl font-medium mb-1.5">
                {{ $t("email") }}
              </h4>
              <NuxtLink
                class="text-base md:text-lg text-light-white"
                :to="`mailto: <EMAIL>`"
                aria-label="GoToMail"
              >
                <EMAIL>
              </NuxtLink>
            </div>
          </div>
          <div class="flex space-x-5 md:space-x-6 item">
            <img
              class="h-[52px] w-[52px] xl:h-[60px] xl:w-[60px]"
              src="/icons/call.svg"
              alt=""
            />
            <div class="details flex flex-col">
              <h4 class="text-xl xl:text-2xl font-medium mb-1.5">
                {{ $t("call") }}
              </h4>
              <NuxtLink
                class="text-base md:text-lg text-light-white"
                :to="`tel:+8801752-202128`"
                aria-label="GoToPhone"
              >
                +8801752-202128
              </NuxtLink>
            </div>
          </div>
        </div>
        <div class="flex space-x-5 md:space-x-6 justify-center">
          <NuxtLink
            target="_blank"
            aria-label="Sk Mobile School Telegram"
            :to="localePath('/')"
          >
            <img
              class="social-icon"
              src="/images/homepage/telegram.svg"
              alt="Sk Mobile School Telegram"
            />
          </NuxtLink>
          <NuxtLink
            to="https://api.whatsapp.com/message/73TPMPASRNJ5C1"
            target="_blank"
            aria-label="whatsapp"
          >
            <SvgWhatsapp class="social-icon" />
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.contact-wrapper {
  @apply justify-center lg:justify-between;
}
.appointment-address {
  height: auto;
  width: 610px;
  min-height: 634px;
}
.details {
  max-width: 230px;
}
.social-icon {
  @apply w-[52px] h-[52px];
}
@media (min-width: 2048px) and (max-width: 2560px) {
  .contact-area {
    padding-left: 14%;
    padding-right: 14%;
  }
}
@media (min-width: 1024px) and (max-width: 1230px) {
  .contact-area {
    padding-left: 7%;
    padding-right: 7%;
  }
  .appointment-address {
    width: 602px;
    min-height: 634px;
  }
  .details {
    max-width: 230px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .appointment-address {
    width: 100%;
    min-height: 550px;
  }
}
@media (min-width: 300px) and (max-width: 767px) {
  .appointment-address {
    width: 100%;
    min-height: 550px;
  }
  .details {
    max-width: 230px;
  }
}

select {
  -webkit-appearance: none;
  appearance: none;
  color: var(--text-color);
  opacity: var(--text-opacity);
}
option:not(:first-of-type) {
  color: #000;
}

.select-wrapper {
  position: relative;
}

.select-toggle {
  color: #000;
  height: 24px;
  @apply absolute top-4 right-3 pointer-events-none;
  padding-left: 3px !important;
}
</style>

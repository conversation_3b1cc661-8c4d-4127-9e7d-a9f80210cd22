<script setup>
import { ErrorMessage, Field, Form } from "vee-validate";

const { validateEmail } = useValidation();
const { apiBaseUrl } = useUrls();
const config = useRuntimeConfig();
const { $toast } = useNuxtApp();
const router = useRouter();
const localePath = useLocalePath();

const email = ref();
const recaptchaRef = ref(null);
const loading = ref(false);

const handleForgetPassword = async () => {
  loading.value = true;
  $toast("clear");

  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "PASSWORD_RESET",
      }
    );

    if (captchaToken) {
      try {
        const data = await $fetch(`${apiBaseUrl}/auth/forgot-password`, {
          method: "POST",
          body: {
            email: email.value,
            captcha_token: captchaToken,
          },
        });
        $toast("success", {
          message: `${data.message}`,
          className: "toasted-bg-success",
        });
        router.push(localePath(`/auth/verify-forgot-otp?email=${email.value}`));
      } catch (e) {
        $toast("error", {
          message: `${e.data.message}`,
          className: "toasted-bg-alert",
        });
      } finally {
        loading.value = false;
      }
    }
  }
};

onMounted(() => {
  watchEffect(() => {
    if (window.grecaptcha && window.grecaptcha.enterprise) {
      recaptchaRef.value = window.grecaptcha.enterprise;
    }
  });
});
</script>

<template>
  <main class="custom-container">
    <section class="mt-20 mb-32 flex items-center justify-center">
      <div
        class="card-shadow w-[660px] px-2.5 xs:px-4 pt-8 pb-10 md:pt-20 md:pb-28 md:px-10 font-semibold text-lg"
      >
        <h1 class="text-2xl font-semibold mb-10">
          {{ $t("forgot_pass_text") }}
        </h1>
        <Form
          @submit="handleForgetPassword"
          @keypress.enter="handleForgetPassword"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <Field
            v-model="email"
            name="email"
            type="email"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg opacity-50"
            :placeholder="$t('email_address')"
            :rules="validateEmail"
          />
          <ErrorMessage class="error-message block" name="email" />
          <button
            class="px-4 h-[50px] text-primary-red border border-primary-red text-lg font-semibold rounded hover:bg-primary-red hover:text-white disabled:opacity-70 disabled:hover:bg-transparent disabled:hover:text-primary-red mt-20"
            type="submit"
            :disabled="!meta.valid || loading"
          >
            {{ $t("continue") }}
          </button>
        </Form>
        <div class="flex items-center justify-center mt-20">
          <p>
            {{ $t("no_account") }}
            <nuxt-link to="/auth/login" class="text-primary-red"
              >{{ $t("register_now") }}
            </nuxt-link>
          </p>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup lang="ts">
import { Form, Field, ErrorMessage } from "vee-validate";
import { useDashboardStore } from "~/stores/dashboard";
import FingerprintJS from "@fingerprintjs/fingerprintjs";

const { LOGIN, CHANGE_DEVICE, VERIFY_CHANGE_DEVICE } = useUrls();
const { validateEmail, validatePassword } = useValidation();
const { setAllNotifications } = useDashboardStore();
const { setAuthCookies, setDeviceId, tokenCookie } = useAuth();
const nuxtApp = useNuxtApp();
const config = useRuntimeConfig();
const router = useRouter();
const redirectUrl = useCookie("redirectUrl");
const deviceIdCookie = useCookie("deviceId");
const localePath = useLocalePath();
const { joinToChannel } = useNotification();
const { t } = useI18n();

const rememberMeCookie: any = useCookie("rememberMe", {
  watch: true,
});
const userEmail = ref(
  rememberMeCookie.value ? rememberMeCookie.value.email : ""
);
const userPass = ref(
  rememberMeCookie.value ? rememberMeCookie.value.password : ""
);
const showPassword = ref(false);
const deviceId = ref("");

const generateDeviceFingerprint = async () => {
  const fpPromise = FingerprintJS.load();
  const fp = await fpPromise;
  const result = await fp.get();
  deviceId.value = result.visitorId
};

const recaptchaRef = ref<any>(null);
const loading = ref(false);
if (process.client) {
  nuxtApp.$toast("clear");
}
const handleLogin = async () => {
  loading.value = true;
  if (recaptchaRef.value) {
    const captchaToken = await recaptchaRef.value.execute(
      config.public.recaptchaKey,
      {
        action: "LOGIN",
      }
    );

    if (captchaToken) {
      try {
        const { token, user, message }: any = await $fetch(LOGIN, {
          method: "POST",
          body: {
            email: userEmail.value,
            password: userPass.value,
            captcha_token: captchaToken,
            device_id: deviceIdCookie.value
              ? deviceIdCookie.value
              : deviceId.value,
          },
        });

        if (token) {
          await setAuthCookies(token);
          if (tokenCookie.value) {
            if (user && user.id) {
              setTimeout(() => {
                joinToChannel(user.id);
                setAllNotifications();
              }, 1000);
            }

            nuxtApp.$toast("success", {
              message: t("messages.login_success"),
              className: "toasted-bg-success",
            });

            setTimeout(() => {
              if (
                redirectUrl.value &&
                redirectUrl.value !== null &&
                redirectUrl.value !== undefined
              ) {
                router.push(localePath(`${redirectUrl.value}`));
              } else {
                router.push(localePath("/"));
              }
            }, 1000);
          } else {
            nuxtApp.$toast("error", {
              message: message,
              className: "toasted-bg-alert",
            });
          }
        } else {
          nuxtApp.$toast("error", {
            message: message,
            className: "toasted-bg-alert",
          });
        }
      } catch (error: any) {
        nuxtApp.$toast("error", {
          message: error?.response?._data?.message.password
            ? error?.response?._data?.message.password
            : error?.response?._data?.message.email
            ? error?.response?._data?.message.email
            : error?.response?._data?.message,
          className: "toasted-bg-alert",
        });
        if (
          error?.response?.status === 401 ||
          error?.response?.status === 404
        ) {
          userEmail.value = "";
          userPass.value = "";
          setTimeout(() => {
            router.push(localePath("/auth/security"));
          }, 200);
        }
      } finally {
        loading.value = false;
      }
    }
  }
};
const setRememberMeCookie = (email: string, password: string) => {
  rememberMeCookie.value = { email, password };
};

onMounted(() => {
  if (tokenCookie.value) {
    router.push(localePath("/"));
  }
  generateDeviceFingerprint();

  // Watch for the script to be loaded
  watchEffect(() => {
    if (window.grecaptcha && window.grecaptcha.enterprise) {
      recaptchaRef.value = window.grecaptcha.enterprise;
    }
  });
  window.scrollTo(0, 0);
});
</script>

<template>
  <main class="custom-container">
    <section class="mt-20 mb-32 flex items-center justify-center">
      <div
        class="card-shadow w-[660px] px-2.5 xs:px-4 pt-8 pb-10 md:pt-20 md:pb-28 md:px-10 font-semibold text-lg"
      >
        <h1 class="text-2xl font-semibold mb-10">{{ $t("welcome") }}</h1>
        <Form
          @submit="handleLogin"
          @keypress.enter="handleLogin"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <Field
            v-model="userEmail"
            name="email"
            type="email"
            class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
            :placeholder="$t('email_address')"
            :rules="validateEmail"
          />
          <ErrorMessage class="error-message" name="email" />
          <div class="relative">
            <Field
              v-model="userPass"
              name="password"
              :type="showPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg font-semibold"
              :placeholder="$t('password')"
              :rules="validatePassword"
            />
            <div
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPassword"
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                @click="showPassword = !showPassword"
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message" name="password" />

          <div class="flex flex-col text-sm md:text-lg gap-3">
            <div class="flex flex-row items-center justify-between">
              <div class="flex items-center gap-3">
                <input
                  type="checkbox"
                  class="w-6 h-6 md:w-[30px] md:h-[30px]"
                  @change="setRememberMeCookie(userEmail, userPass)"
                />
                <label for="rememberMe">{{ $t("remember_me") }}</label>
              </div>
              <div class="flex flex-col gap-3 items-end">
                <NuxtLink
                  :to="localePath('/auth/forgot-password')"
                  class="opacity-50 hover:opacity-100"
                  >{{ $t("forgot_password") }}</NuxtLink
                >
              </div>
            </div>
            <div class="hidden flex-row items-center justify-between">
              <span></span>
              <NuxtLink
                :to="localePath('/auth/security')"
                class="cursor-pointer w-60 text-right opacity-50 hover:opacity-100"
              >
                Request for new device
              </NuxtLink>
            </div>
            <div class="flex flex-row items-center justify-between">
              <span></span>
              <NuxtLink
                :to="localePath('/auth/resend-mail')"
                class="cursor-pointer w-60 text-right opacity-50 hover:opacity-100"
              >
                {{ $t("verification_mail") }}
              </NuxtLink>
            </div>
          </div>

          <button
            class="px-4 h-[50px] text-primary-red border border-primary-red text-lg font-semibold rounded hover:bg-primary-red hover:text-white disabled:opacity-70 disabled:hover:bg-transparent disabled:hover:text-primary-red mt-20"
            type="submit"
            :disabled="!meta.valid || loading"
          >
            {{ $t("sign_in") }}
          </button>
        </Form>
        <div class="flex items-center justify-center mt-20">
          <p>
            {{ $t("no_account") }}
            <nuxt-link to="/auth/register" class="text-primary-red"
              >{{ $t("register_now") }}
            </nuxt-link>
          </p>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
.verifyInput {
  display: block;
  outline: none;
  border: none;
  height: 2em;
  font-size: 16px;
  margin-bottom: 1px;
  border-bottom: 1px solid #333;
}

.verifyInput:focus {
  border-bottom: 1px solid #0572ce;
  box-shadow: 0 1px 0 0 #0572ce;
}
</style>

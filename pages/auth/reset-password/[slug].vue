<script setup lang-="ts">
import { ErrorMessage, Field, Form } from "vee-validate";

const { validatePassword } = useValidation();
const { apiBaseUrl } = useUrls();
const { $toast } = useNuxtApp();
const route = useRoute();
const { t } = useI18n();
const router = useRouter();
const localePath = useLocalePath();

const email = computed(() => route.query?.email || "");
const token = computed(() => route.params?.slug || "");
const newPassword = ref();
const confirmNewPassword = ref();
const showPassword = ref(false);
const showPasswordTwo = ref(false);
const loading = ref(false);

const handleResetPassword = async () => {
  loading.value = true;
  $toast("clear");
  if (newPassword.value === confirmNewPassword.value) {
    try {
      const data = await $fetch(
        `${apiBaseUrl}/auth/forgot-password/reset-password`,
        {
          method: "POST",
          body: {
            email: email.value,
            remember_token: token.value,
            password: newPassword.value,
            password_confirmation: confirmNewPassword.value,
          },
        }
      );

      $toast("success", {
        message: `${data.message}`,
        className: "toasted-bg-success",
      });
      router.push(localePath("/"));
    } catch (error) {
      $toast("error", {
        message: `${error.data.message}`,
        className: "toasted-bg-alert",
      });
    } finally {
      loading.value = false;
    }
  } else {
    $toast("error", {
      message: t("messages.password_not_match"),
      className: "toasted-bg-alert",
    });
    loading.value = false;
  }
};
</script>

<template>
  <main class="custom-container">
    <section class="mt-20 mb-32 flex items-center justify-center">
      <div
        class="card-shadow w-[660px] px-2.5 xs:px-4 py-8 md:py-20 md:px-10 font-semibold text-lg"
      >
        <h1 class="text-2xl font-semibold mb-10">
          {{ $t("reset_pass_title") }}
        </h1>
        <Form
          @submit="handleResetPassword"
          @keypress.enter="handleResetPassword"
          v-slot="{ meta }"
          class="space-y-5"
        >
          <div class="relative">
            <Field
              v-model="newPassword"
              name="newPassword"
              :type="showPassword ? 'text' : 'password'"
              class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg opacity-50"
              :placeholder="$t('new_pass')"
              :rules="validatePassword"
            />
            <div
              @click="showPassword = !showPassword"
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPassword"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message block" name="newPassword" />

          <div class="relative">
            <Field
              v-model="confirmNewPassword"
              name="confirmNewPassword"
              :type="showPasswordTwo ? 'text' : 'password'"
              class="w-full px-6 p-2 border border-black outline-none rounded-[10px] text-lg opacity-50"
              :placeholder="$t('confirm_new_password')"
              :rules="validatePassword"
            />
            <div
              @click="showPasswordTwo = !showPasswordTwo"
              class="absolute top-0 bottom-0 my-auto right-3 w-5 flex items-center"
            >
              <IconsEyeHide
                v-if="!showPasswordTwo"
                class="text-[#03022932] cursor-pointer"
              />
              <IconsEyeShow
                v-else
                class="text-[#03022932] cursor-pointer w-[18px]"
              />
            </div>
          </div>
          <ErrorMessage class="error-message block" name="confirmNewPassword" />

          <button
            class="px-4 h-[50px] text-primary-red border border-primary-red text-lg font-semibold rounded hover:bg-primary-red hover:text-white disabled:opacity-70 disabled:hover:bg-transparent disabled:hover:text-primary-red mt-20"
            type="submit"
            :disabled="!meta.valid || loading"
          >
            {{ $t("continue") }}
          </button>
        </Form>
      </div>
    </section>
  </main>
</template>

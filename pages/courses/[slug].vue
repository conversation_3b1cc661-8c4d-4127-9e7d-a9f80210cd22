<script setup lang="ts">
// import { Carousel, Slide } from "vue3-carousel";

const { $toast } = useNuxtApp();
const localePath = useLocalePath();
const { t } = useI18n();

const settings: any = {
  itemsToShow: 1,
  snapAlign: "center",
};
const breakpoints: any = {
  700: {
    itemsToShow: 3.5,
    snapAlign: "start",
  },
  1280: {
    itemsToShow: 4,
    snapAlign: "start",
  },
};

const { apiBaseUrl } = useUrls();
const route = useRoute();
const tokenCookie = useCookie("token");

const { data: courseDetailsRes }: any = useFetch(
  `${apiBaseUrl}/courses/${route.params.slug}`
);
const courseDetails = computed(() => courseDetailsRes.value?.data);

const allSubscriptions: any = ref([]);
const selectedSubscription = ref("");

const oldCardData: any = ref([]);
const showSubscriptionModal = ref(false);
const selectedProduct = ref("");
const selectedItemId = ref(0);
const addToCart = async (product: any, itemId: number) => {
  showSubscriptionModal.value = !showSubscriptionModal.value;

  selectedProduct.value = product;
  selectedItemId.value = itemId;

  const cartDataCookie: any = useCookie("cartData");

  if (selectedSubscription.value !== "") {
    if (tokenCookie.value) {
      try {
        const checkItemExist = ref(false);
        const { data }: any = await useFetch(`${apiBaseUrl}/carts`, {
          headers: {
            Authorization: `Bearer ${tokenCookie.value}`,
          },
        });
        data.value.data.forEach((singleItem: any) => {
          if (singleItem.item.id === itemId) {
            checkItemExist.value = true;
            $toast("error", {
              message: t("messages.already_added_to_cart"),
              className: "toasted-bg-alert",
            });
          }
        });

        if (!checkItemExist.value) {
          const { data, error }: any = await useFetch(`${apiBaseUrl}/carts/add`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${tokenCookie.value}`,
            },
            body: {
              item_id: itemId,
              subscription_id: selectedSubscription.value,
            },
          });
          if (!error.value && data.value ) {
            $toast("clear");
            $toast("success", {
              message: data.value.message,
              className: "toasted-bg-success",
            });
          }

          return data.value.cart;
        }
      } catch (err) {
        console.log(err);
      }
    } else {
      const addNewProduct = {
        id: product.id,
        item_category_id: product.item_category_id,
        title: product.title,
        banner_url: product.banner_url,
        price: product.price,
        special_price: product.special_price,
        subscription_id: selectedSubscription.value,
        monthly_service_charge: product.monthly_service_charge,
      };

      if (cartDataCookie.value?.length > 0) {
        const isExist = cartDataCookie.value.find(
          (singleCartData: any) => singleCartData.id === addNewProduct.id
        );

        if (isExist) {
          $toast("error", {
            message: t("messages.already_added_to_cart"),
            className: "toasted-bg-success",
          });
        } else {
          cartDataCookie.value = JSON.stringify(cartDataCookie.value);

          oldCardData.value = JSON.parse(cartDataCookie.value);
          cartDataCookie.value = JSON.stringify([]);
          oldCardData.value.push(addNewProduct);
          cartDataCookie.value = JSON.stringify(oldCardData.value);
          $toast("success", {
            message: t("messages.added_to_cart"),
            className: "toasted-bg-success",
          });
        }
      } else {
        oldCardData.value.push(addNewProduct);
        cartDataCookie.value = JSON.stringify(oldCardData.value);
        $toast("success", {
          message: t("messages.added_to_cart"),
          className: "toasted-bg-success",
        });
      }
    }
  }
};

const handleSubscription = (id: any) => {
  selectedSubscription.value = id;
  addToCart(selectedProduct.value, selectedItemId.value);
};

// for subscription modal
const getSubscription = async () => {
  const { data }: any = await useFetch(`${apiBaseUrl}/subscriptions/video`, {});

  allSubscriptions.value = data._rawValue?.data;
};
await getSubscription();

onMounted(() => {});
</script>

<template>
  <div class="custom-container">
    <div
      v-if="courseDetails"
      class="flex flex-col lg:flex-row pt-[80px] pb-[80px] lg:pb-[150px]"
    >
      <img
        :src="courseDetails.banner_url"
        class="lg:min-w-[520px] lg:max-w-[625px] h-[425px]"
        width="100%"
        alt="courseDetails.title"
      />
      <div class="pl-[1px] md:!pl-[25px] lg:!pl-[125px]">
        <h1
          class="pt-[20px] md:pt-[25px] text-lg md:text-[40px] font-bold text-primary-red leading-[3rem] break-all"
        >
          {{ courseDetails.title }}
        </h1>
        <div
          class="font-normal text-base lg:text-2xl pt-5"
          v-html="courseDetails.description"
        ></div>
        <!-- <div v-if="courseDetails.special_price" class="flex items-center pt-2">
          <p class="text-[30px] line-through text-[#ADA7A7] font-bold">৳</p>
          <p class="text-[30px] line-through text-[#ADA7A7] mr-3">
            {{ courseDetails.price }}
          </p>
          <p class="text-[30px]">৳</p>
          <p class="text-[30px]">
            {{ courseDetails.special_price }}
          </p>
        </div>
        <div v-else class="flex pt-5">
          <p class="text-[30px]">৳</p>
          <p class="text-[30px]">
            {{ courseDetails.price }}
          </p>
        </div> -->

        <div class="pt-10">
          <!-- <button
            class="p-3 bg-black text-white"
            @click="addToCart(courseDetails, courseDetails.id)"
          >
            Add to Cart
          </button> -->
          <NuxtLink :to="localePath(`/precheckout/${route.params.slug}`)">
            <button class="p-3 bg-black text-white rounded-md">
              View Course Details
            </button>
          </NuxtLink>
        </div>
      </div>
    </div>

    <div
      v-if="courseDetails"
      class="pb-40 text-center md:text-left flex flex-col"
    >
      <p class="text-[30px] text-[#EC1F27] text-large py-3">Course Details</p>
      <hr class="bg-gray-400 h-[2px]" />
      <div
        class="font-normal text-base lg:text-2xl pt-5"
        v-html="courseDetails.description"
      ></div>
      <NuxtLink
        :to="localePath(`/precheckout/${route.params.slug}`)"
        class="self-center"
      >
        <button class="p-3 bg-black text-white rounded-md">
          View Course Details
        </button>
      </NuxtLink>
    </div>
    <!-- <div class="py-5 md:py-20 hidden">
      <p class="text-[30px] text-[#EC1F27] text-large pt-3 pb-14">
        Related Courses
      </p>

      <Carousel v-bind="settings" :breakpoints="breakpoints">
        <Slide v-for="(slider, index) in relatedCourse" :key="slider.id">
          <div class="carousel__item" :class="index === 0 ? 'pr-2' : 'px-2'">
            <NuxtLink :to="`/courses/${slider.slug}`">
              <img
                v-if="slider.banner_url"
                class="w-[413px] h-[286px]"
                :src="slider.banner_url"
                :alt="slider.title"
              />
            </NuxtLink>
            <p
              class="flex justify-start text-[#EC1F27] font-semibold text-lg md:text-[30px] pt-5"
            >
              {{ slider.title }}
            </p>
            <div class="flex flex-col justify-start">
              <div v-if="slider.special_price" class="flex items-center">
                <p class="text-xl line-through text-[#ADA7A7] font-bold">৳</p>
                <p class="text-xl line-through text-[#ADA7A7] mr-3">
                  {{ slider.price }}
                </p>
                <p class="text-xl">৳</p>
                <p class="text-xl">
                  {{ slider.special_price }}
                </p>
              </div>
              <div v-else class="flex">
                <p class="text-[30px]">৳</p>
                <p class="text-[30px]">
                  {{ courseDetails.price }}
                </p>
              </div>

              <button
                class="w-[125px] h-[40px] bg-black text-white mb-5 md:mb-0 mt-1 md:mt-5"
                @click="addToCart(courseDetails, courseDetails.id)"
              >
                Add to Cart
              </button>
            </div>
          </div>
        </Slide>

        <template #addons>
          <div class="mt-7">
            <Pagination />
          </div>
        </template>
      </Carousel>
    </div> -->

    <div
      v-if="showSubscriptionModal"
      class="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
      ></div>

      <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div
          class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
        >
          <div
            class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-[80%]"
          >
            <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start justify-center">
                <div
                  class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left relative"
                >
                  <div
                    class="text-base font-semibold leading-6 text-gray-900 absolute right-0 top-0 cursor-pointer"
                    id="modal-title"
                    @click="showSubscriptionModal = false"
                  >
                    X
                  </div>
                  <div class="mt-2">
                    <div
                      class="custom-container pt-10 md:!pt-[70px] xd:!pt-20 pb-36"
                    >
                      <h2
                        class="text-[32px] md:!text-[40px] xl:!text-6xl font-bold text-center text-primary-red"
                      >
                        Select a subscription package
                      </h2>
                      <div
                        class="pt-10 flex flex-wrap justify-center items-start gap-5 lg:!gap-[54px]"
                      >
                        <div
                          v-for="(item, index) in allSubscriptions"
                          :key="item.id"
                          class="w-[300px] lg:!w-[300px] rounded-[20px] shadow-[0px_4px_25px_-1px_rgba(0,0,0,0.25)]"
                        >
                          <div class="text-center p-10">
                            <h3
                              class="text-[30px] md:!text-4xl font-bold"
                              :class="
                                index === 0
                                  ? 'text-[#3D3B3B]'
                                  : index === 1
                                  ? 'text-[#404DC0]'
                                  : index === 2
                                  ? 'text-[#63bc3a]'
                                  : 'text-[#EC1F27]'
                              "
                            >
                              {{ item.name }}
                            </h3>
                            <p
                              class="pt-1 pb-6 text-sm font-bold text-[#747474]"
                            >
                              Perfect For {{ item.perfectFor }}
                            </p>
                            <div
                              class="flex justify-center font-bold pb-7 text-black"
                            >
                              <p class="relative flex space-x-2">
                                <span class="text-xl md:!text-2xl">TK</span>
                                <span class="text-4xl md:!text-5xl">{{
                                  item.amount
                                }}</span
                                ><span
                                  class="text-sm whitespace-nowrap self-end"
                                  >/{{ item.durations_as_month }}
                                  {{
                                    item.durations_as_month > 1
                                      ? "months"
                                      : "month"
                                  }}</span
                                >
                              </p>
                            </div>

                            <button
                              class="w-[180px] h-9 rounded-full text-white font-bold"
                              :class="
                                index === 0
                                  ? 'bg-[#3D3B3B]'
                                  : index === 1
                                  ? 'bg-[#404DC0]'
                                  : index === 2
                                  ? 'bg-[#63bc3a]'
                                  : 'bg-[#EC1F27]'
                              "
                              @click="handleSubscription(item.id)"
                            >
                            {{$t("get_started")}}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

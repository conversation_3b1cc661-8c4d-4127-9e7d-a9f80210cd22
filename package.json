{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/vue-fontawesome": "^3.0.3", "@nuxtjs/i18n": "10.1.0", "@nuxtjs/seo": "3.2.2", "@types/node": "^18.19.7", "@types/vue-tel-input": "^2.1.6", "@vueuse/core": "^10.11.1", "@vueuse/nuxt": "^10.11.1", "autoprefixer": "^10.4.14", "dayjs-nuxt": "2.1.11", "postcss": "^8.4.27", "tailwindcss": "^3.3.3"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.2.1", "@photo-sphere-viewer/core": "^5.4.4", "@pinia/nuxt": "^0.5.5", "@popperjs/core": "^2.11.8", "@vee-validate/nuxt": "^4.15.1", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "laravel-echo": "^1.15.3", "lodash.debounce": "^4.0.8", "maska": "^2.1.10", "nuxt": "^3.19.2", "pinia": "^2.3.1", "pusher-js": "^8.3.0", "v-calendar": "^3.1.0", "vue": "^3.5.21", "vue-router": "^4.5.1", "vue-tel-input": "^8.3.1", "vue-toastification": "2.0.0-rc.5", "vue3-carousel": "^0.3.1"}}
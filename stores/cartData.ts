import { defineStore } from 'pinia'

export const useCartDataStore = defineStore("cartData", () => {
  // state
  const allCartDataRef = ref<any>([])
  const cartDataForApiRef = ref<any>([])
  const subscriptionDataForApiRef = ref<any>({})

  // computed
  const allCartData = computed(() => allCartDataRef.value)
  const cartDataForApi = computed(() => cartDataForApiRef.value)
  const subscriptionDataForApi = computed(() => subscriptionDataForApiRef.value)

  // actions
  const setAllCartData = (payload: any[]) => {
    allCartDataRef.value = payload
  }
  const setCartDataForApi = (payload: any[]) => {
    cartDataForApiRef.value = payload
  }
  const setSubscriptionDataForApi = (payload: any) => {
    subscriptionDataForApiRef.value = payload
  }
  return {
    allCartData,
    setAllCartData,

    cartDataForApi,
    setCartDataForApi,

    subscriptionDataForApi,
    setSubscriptionDataForApi,
  }
})
import { defineStore } from "pinia";

export const useAuthStore = defineStore("auth", () => {
  // state
  const showCountryDropdown = ref(false);
  const user = ref(null);

  // getters
  const isLoggedIn = computed(() => !!user.value);

  // actions
  const setCountryDropdown = (value: boolean) => {
    showCountryDropdown.value = value;
  };
  const setUser = (value: any) => {
    user.value = value;
  };

  return {
    showCountryDropdown,
    setCountryDropdown,
    setUser,
    user,
    isLoggedIn,
  };
});

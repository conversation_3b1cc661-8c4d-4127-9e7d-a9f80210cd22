import { defineStore } from "pinia";

export const useIndexStore = defineStore("index", () => {
  // state
  const { COMMON_SETTINGS } = useUrls();
  const subcriptionInfo = ref<string[]>([]);
  const media = ref<any>(null);

  // actions
  const fetchCommonSettings = async () => {
    try {
      const settings = await $fetch<any>(COMMON_SETTINGS);
      if (settings?.data) {
        subcriptionInfo.value = settings?.data?.subcription_info;
      }
    } catch (error) {
      console.log(error);
    }
  };

  const setMedia = (payload: any) => {
    media.value = payload;
  };

  return {
    subcriptionInfo,
    fetchCommonSettings,
    media,
    setMedia,
  };
});

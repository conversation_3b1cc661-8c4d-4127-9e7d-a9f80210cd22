import { useAuthStore } from "~/stores/auth";

export const useAuth = () => {
  const router = useRouter();
  const localePath = useLocalePath();
  const { LOGOUT } = useUrls();
  const { setUser } = useAuthStore();

  const tokenCookie: any = useCookie("token", {
    watch: true,
    maxAge: 2592000,
  });

  const redirectUrl: any = useCookie("redirectUrl", {
    watch: true,
    maxAge: 2592000,
  });

  const deviceId = useCookie("deviceId", {
    maxAge: 2592000,
  });

  const setAuthCookies = async (token: string) => {
    tokenCookie.value = token ? token : "";
  };

  const setRedirectUrl = (currentUrl: string) => {
    redirectUrl.value = currentUrl;
  };

  const setDeviceId = (id: string) => {
    deviceId.value = id;
  };

  const logout = async (token: string) => {
    const tokenCookieAgain = useCookie("token");
    try {
      await $fetch(LOGOUT, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
      return true;
    } catch (error) {
      return false;
    } finally {
      tokenCookieAgain.value = "";
      setRedirectUrl("/");
      setUser(null);
      setTimeout(() => {
        router.replace(localePath("/auth/login"));
      }, 500);
    }
  };

  return {
    tokenCookie,
    setAuthCookies,
    logout,
    redirectUrl,
    setRedirectUrl,
    deviceId,
    setDeviceId,
  };
};

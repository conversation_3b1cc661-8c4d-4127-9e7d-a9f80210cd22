/* Vue-Carousel Pagination */
.carousel__pagination {
  margin: 30px 0 0;
}

.success-student-videos.carousel__pagination {
  margin: 20px 0 0;
}

.carousel__pagination-button::after {
  @apply w-3 h-3 rounded-full bg-white;
  border: 1px solid #ec1f27;
}

.carousel__pagination-button:hover::after {
  background-color: #ec1f27;
}

.carousel__pagination-button--active::after {
  background-color: #ec1f27;
  width: 30px;
}

/* Vue-Carousel Navigation */
.carousel__icon {
  font-size: 40px;
  fill: #ec1f27;
  @apply rounded-full bg-[#FCFCFD];
}
.course-details .carousel__icon {
  fill: white;
  filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  @apply border border-white rounded-full bg-transparent;
}
.special-service .carousel__icon,
.success-report .carousel__icon {
  fill: #ec1f27;
  @apply border border-primary-red rounded-full bg-transparent;
}
.consultation .carousel__icon,
.technicians .carousel__icon {
  fill: black;
}

.blog-navigation.carousel__prev {
  left: -30px;
}
.blog-navigation.carousel__next {
  right: -30px;
}
.special-service.carousel__prev {
  left: -40px;
}
.special-service.carousel__next {
  right: -40px;
}
.success-report.carousel__prev {
  left: -70px;
}
.success-report.carousel__next {
  right: -110px;
}
.course-details.carousel__prev {
  left: 80px;
}
.course-details.carousel__next {
  right: 80px;
}

.carousel__prev,
.carousel__next {
  box-sizing: content-box;
  border-radius: 100%;
  height: 50px;
  width: 50px;
}

.carousel__next--disabled,
.carousel__prev--disabled {
  opacity: 0.5;
}

@media (max-width: 425px) {
  .carousel__prev,
  .carousel__next {
    height: 45px;
    width: 45px;
    margin: 0 5px;
  }
}

@media (max-width: 1023px) {
  .success-report.carousel__prev {
    left: -30px;
  }
  .success-report.carousel__next {
    right: -30px;
  }
  .course-details.carousel__prev {
    left: 0px;
  }
  .course-details.carousel__next {
    right: 0px;
  }
}
@media (min-width: 1024px) and (max-width: 1280px) {
  .success-report.carousel__prev {
    left: -70px;
  }
  .success-report.carousel__next {
    right: -70px;
  }
}

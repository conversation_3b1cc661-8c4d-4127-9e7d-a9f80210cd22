@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Roboto", sans-serif;
  }
  body {
    background-color: #fcfcfd;
    box-sizing: border-box;
    top: 0px !important;
  }
}

@layer components {
  .custom-container {
    @apply px-4 md:px-8 lg:px-[4%] dx:px-0 dx:max-w-[1280px] 2xl:max-w-[1400px] mx-auto;
  }
  .view-more-btn {
    @apply w-full bg-[#FEE] rounded-[10px] font-semibold text-sm xl:text-base text-primary-red leading-4 text-center p-[13px] outline-none inline-block;
  }
  .red-button {
    @apply flex justify-center items-center rounded-full bg-primary-red opacity-100 px-6 pb-2 pt-2.5 text-base font-bold leading-normal text-white hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)];
  }
  .heading-primary {
    @apply text-primary-red text-3xl lg:text-4xl font-bold;
  }
  .text-primary {
    @apply text-base lg:text-lg text-black;
  }
  .main-heading {
    @apply text-4xl xl:text-6xl text-primary-red font-bold leading-[54px];
  }
  .sub-heading {
    @apply text-3xl	text-primary-red font-bold leading-9;
  }
  .text-large {
    @apply text-xl xl:text-2xl text-black font-medium leading-8;
  }
  .text-small {
    @apply xl:text-lg first:text-black font-normal leading-7;
  }
  .nav-link {
    @apply text-black font-semibold hover:text-primary-red focus:text-primary-red disabled:text-black/30 lg:px-2 [&.active]:text-primary-red;
  }
  .nav-link-custom {
    @apply text-black font-semibold hover:text-primary-red disabled:text-black/30 lg:px-2 [&.active]:text-primary-red;
  }
  .nav-link-footer {
    @apply font-semibold hover:text-primary-red focus:text-primary-red disabled:text-black/30 [&.active]:text-primary-red;
  }
  .error-message {
    @apply text-primary-red font-normal text-base !mt-1 ml-1;
  }
  .card-shadow {
    box-shadow: 0px -4px 4px 0px #00000040, 0px 4px 4px 0px #00000040;
  }
}

/* for page trasition*/
.page-enter-active,
.page-leave-active {
  transition: opacity 0.5s ease;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
}

.layout-enter-active,
.layout-leave-active {
  transition: opacity 0.5s ease;
}
.layout-enter-from,
.layout-leave-to {
  opacity: 0;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
/* Animate Button */
.animate-button {
  animation: changeBgColor 2s infinite;
}
@keyframes changeBgColor {
  0% {
    background-color: #000000;
  }
  33% {
    background-color: #f7088c;
  }
  66% {
    background-color: #ec1f27;
  }
  100% {
    background-color: #000000;
  }
}
